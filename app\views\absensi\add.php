<?php
// Halaman tambah absensi siswa
?>

<div class="container-fluid py-4">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body bg-primary text-white rounded">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    <i class="bi bi-calendar-plus" style="font-size: 2.5rem;"></i>
                                </div>
                                <div>
                                    <h4 class="mb-1 fw-bold">Tambah Absensi</h4>
                                    <p class="mb-0 opacity-75">
                                        <i class="bi bi-person me-1"></i>
                                        <?= htmlspecialchars($siswa['nama_lengkap']) ?>
                                        <span class="mx-2">•</span>
                                        <i class="bi bi-mortarboard me-1"></i>
                                        <?= htmlspecialchars($siswa['nama_kelas']) ?>
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 text-md-end">
                            <a href="/siswa-app/public/siswa/detail/<?= $siswa['id_siswa'] ?>" class="btn btn-light btn-lg">
                                <i class="bi bi-arrow-left me-2"></i>
                                Kembali
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Messages -->
    <?php if ($success): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle me-2"></i>
            <?= htmlspecialchars($success) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle me-2"></i>
            <?= htmlspecialchars($error) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Form Section -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0 text-dark">
                        <i class="bi bi-calendar-plus me-2"></i>
                        Form Tambah Absensi
                    </h5>
                </div>
                <div class="card-body p-4">
                    <form method="POST" action="/siswa-app/public/absensi/create" enctype="multipart/form-data" id="addAbsensiForm">
                        <input type="hidden" name="csrf_token" value="<?= $csrf_token ?>">
                        <input type="hidden" name="siswa_id" value="<?= $siswa['id_siswa'] ?>">

                        <!-- Date and Type -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="tanggal" class="form-label fw-semibold">
                                        <i class="bi bi-calendar me-1"></i>
                                        Tanggal <span class="text-danger">*</span>
                                    </label>
                                    <input type="date" 
                                           class="form-control form-control-lg" 
                                           id="tanggal" 
                                           name="tanggal" 
                                           max="<?= date('Y-m-d') ?>"
                                           required>
                                    <div class="form-text">
                                        <i class="bi bi-info-circle me-1"></i>
                                        Pilih tanggal ketidakhadiran
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="jenis_ketidakhadiran" class="form-label fw-semibold">
                                        <i class="bi bi-list me-1"></i>
                                        Jenis Ketidakhadiran <span class="text-danger">*</span>
                                    </label>
                                    <select class="form-select form-select-lg" id="jenis_ketidakhadiran" name="jenis_ketidakhadiran" required>
                                        <option value="">Pilih Jenis</option>
                                        <?php foreach ($jenis_options as $value => $option): ?>
                                            <option value="<?= $value ?>" data-color="<?= $option['color'] ?>" data-icon="<?= $option['icon'] ?>">
                                                <?= $option['label'] ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                    <div class="form-text">
                                        <i class="bi bi-info-circle me-1"></i>
                                        Pilih jenis ketidakhadiran yang sesuai
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Description -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="form-group">
                                    <label for="keterangan" class="form-label fw-semibold">
                                        <i class="bi bi-chat-text me-1"></i>
                                        Keterangan
                                    </label>
                                    <textarea class="form-control" 
                                              id="keterangan" 
                                              name="keterangan" 
                                              rows="4" 
                                              placeholder="Masukkan keterangan detail tentang ketidakhadiran..."></textarea>
                                    <div class="form-text">
                                        <i class="bi bi-info-circle me-1"></i>
                                        Berikan keterangan yang jelas tentang alasan ketidakhadiran
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- File Upload -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="form-group">
                                    <label for="surat_keterangan" class="form-label fw-semibold">
                                        <i class="bi bi-paperclip me-1"></i>
                                        Surat Keterangan
                                    </label>
                                    <input type="file" 
                                           class="form-control" 
                                           id="surat_keterangan" 
                                           name="surat_keterangan" 
                                           accept="image/*,.pdf">
                                    <div class="form-text">
                                        <i class="bi bi-info-circle me-1"></i>
                                        Upload surat dokter, surat ijin, atau dokumen pendukung lainnya (JPG, PNG, PDF, max 5MB)
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Time (Optional) -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="jam_masuk" class="form-label fw-semibold">
                                        <i class="bi bi-clock me-1"></i>
                                        Jam Masuk (Opsional)
                                    </label>
                                    <input type="time" 
                                           class="form-control" 
                                           id="jam_masuk" 
                                           name="jam_masuk">
                                    <div class="form-text">
                                        <i class="bi bi-info-circle me-1"></i>
                                        Jika siswa masuk terlambat
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="jam_keluar" class="form-label fw-semibold">
                                        <i class="bi bi-clock me-1"></i>
                                        Jam Keluar (Opsional)
                                    </label>
                                    <input type="time" 
                                           class="form-control" 
                                           id="jam_keluar" 
                                           name="jam_keluar">
                                    <div class="form-text">
                                        <i class="bi bi-info-circle me-1"></i>
                                        Jika siswa pulang lebih awal
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Jenis Information -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="card bg-light border-0">
                                    <div class="card-body">
                                        <h6 class="card-title">
                                            <i class="bi bi-info-circle me-2"></i>
                                            Informasi Jenis Ketidakhadiran
                                        </h6>
                                        <div class="row">
                                            <?php foreach ($jenis_options as $value => $option): ?>
                                            <div class="col-md-4 mb-3">
                                                <div class="d-flex align-items-start">
                                                    <div class="me-3">
                                                        <span class="badge bg-<?= $option['color'] ?> p-2">
                                                            <i class="<?= $option['icon'] ?>"></i>
                                                        </span>
                                                    </div>
                                                    <div>
                                                        <h6 class="mb-1"><?= $option['label'] ?></h6>
                                                        <small class="text-muted"><?= $option['description'] ?></small>
                                                    </div>
                                                </div>
                                            </div>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-between align-items-center pt-3 border-top">
                                    <div>
                                        <a href="/siswa-app/public/siswa/detail/<?= $siswa['id_siswa'] ?>" class="btn btn-outline-secondary btn-lg">
                                            <i class="bi bi-x-circle me-2"></i>
                                            Batal
                                        </a>
                                    </div>
                                    <div>
                                        <button type="submit" class="btn btn-primary btn-lg px-5" id="submitBtn">
                                            <i class="bi bi-calendar-plus me-2"></i>
                                            Simpan Absensi
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Custom styling for add absensi page */
.form-control, .form-select {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.15);
    transform: translateY(-1px);
}

.form-control-lg, .form-select-lg {
    padding: 0.75rem 1rem;
    font-size: 1.1rem;
}

.form-label {
    color: #495057;
    margin-bottom: 0.75rem;
}

.form-label i {
    color: #007bff;
}

.card {
    border-radius: 12px;
    overflow: hidden;
}

.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.btn-lg {
    padding: 0.75rem 2rem;
}

/* Form group spacing */
.form-group {
    margin-bottom: 1.5rem;
}

/* Badge styling */
.badge {
    font-size: 0.75rem;
    font-weight: 500;
}

/* Loading state */
.btn.loading {
    pointer-events: none;
    opacity: 0.7;
}

.btn.loading i {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
</style>

<script>
// Form validation and enhancement
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('addAbsensiForm');
    const submitBtn = document.getElementById('submitBtn');
    
    // Form submission handling
    form.addEventListener('submit', function(e) {
        // Show loading state
        submitBtn.classList.add('loading');
        submitBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>Menyimpan...';
        submitBtn.disabled = true;
    });
    
    // Date validation
    const tanggalInput = document.getElementById('tanggal');
    tanggalInput.addEventListener('change', function() {
        const selectedDate = new Date(this.value);
        const today = new Date();
        
        if (selectedDate > today) {
            this.setCustomValidity('Tanggal tidak boleh di masa depan');
        } else {
            this.setCustomValidity('');
        }
    });
    
    // File upload validation
    const fileInput = document.getElementById('surat_keterangan');
    fileInput.addEventListener('change', function() {
        const file = this.files[0];
        if (file) {
            const maxSize = 5 * 1024 * 1024; // 5MB
            const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'application/pdf'];
            
            if (file.size > maxSize) {
                alert('Ukuran file terlalu besar. Maksimal 5MB');
                this.value = '';
                return;
            }
            
            if (!allowedTypes.includes(file.type)) {
                alert('Tipe file tidak diizinkan. Gunakan JPG, PNG, GIF, atau PDF');
                this.value = '';
                return;
            }
        }
    });
});
</script>
