<?php
require_once __DIR__ . '/../models/User.php';
require_once __DIR__ . '/../helpers/Security.php';
require_once __DIR__ . '/../helpers/SimpleSessionManager.php';

class AuthController {
    private $userModel;
    private $sessionManager;

    public function __construct() {
        $this->userModel = new User();
        $this->sessionManager = new SimpleSessionManager();
    }
    
    /**
     * Show login form
     */
    public function login() {
        // Redirect if already logged in
        if ($this->sessionManager->isValidSession()) {
            header('Location: /siswa-app/public/dashboard');
            exit;
        }
        
        $data = [
            'title' => 'Login',
            'csrf_token' => Security::generateCSRFToken(),
            'error' => $_SESSION['login_error'] ?? null
        ];
        
        // Clear error message
        unset($_SESSION['login_error']);
        
        $this->view('auth/login', $data);
    }
    
    /**
     * Process login
     */
    public function processLogin() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Location: /siswa-app/public/login');
            exit;
        }

        // Verify CSRF token
        $csrfToken = $_POST['csrf_token'] ?? '';
        if (!Security::verifyCSRFToken($csrfToken)) {
            $_SESSION['login_error'] = 'Token keamanan tidak valid.';
            header('Location: /siswa-app/public/login');
            exit;
        }
        
        // Sanitize input
        $username = Security::sanitizeInput($_POST['username'] ?? '');
        $password = $_POST['password'] ?? '';
        $remember = isset($_POST['remember']);
        
        // Validate input
        if (empty($username) || empty($password)) {
            $_SESSION['login_error'] = 'Username dan password wajib diisi.';
            header('Location: /siswa-app/public/login');
            exit;
        }
        
        // Attempt authentication
        $result = $this->userModel->authenticate($username, $password);
        
        if ($result['success']) {
            // Login successful
            $this->sessionManager->loginUser($result['user']);
            
            // Set remember me cookie if requested
            if ($remember) {
                $this->setRememberMeCookie($result['user']['id']);
            }
            
            // Redirect to intended page or dashboard
            $redirectTo = $_SESSION['intended_url'] ?? '/siswa-app/public/dashboard';
            unset($_SESSION['intended_url']);

            header('Location: ' . $redirectTo);
            exit;
        } else {
            // Login failed
            $_SESSION['login_error'] = $result['message'];
            header('Location: /siswa-app/public/login');
            exit;
        }
    }
    
    /**
     * Logout user
     */
    public function logout() {
        // Clear remember me cookie
        $this->clearRememberMeCookie();
        
        // Logout user
        $this->sessionManager->logoutUser();
        
        header('Location: /siswa-app/public/login');
        exit;
    }
    
    /**
     * Show registration form (admin only)
     */
    public function register() {
        Security::requireAuth();
        Security::requireRole('admin');
        
        $data = [
            'title' => 'Tambah User',
            'csrf_token' => Security::generateCSRFToken(),
            'success' => $_SESSION['register_success'] ?? null,
            'error' => $_SESSION['register_error'] ?? null
        ];
        
        // Clear messages
        unset($_SESSION['register_success'], $_SESSION['register_error']);
        
        $this->view('auth/register', $data);
    }
    
    /**
     * Process registration
     */
    public function processRegister() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Location: /siswa-app/public/register');
            exit;
        }
        
        Security::requireAuth();
        Security::requireRole('admin');
        
        // Verify CSRF token
        $csrfToken = $_POST['csrf_token'] ?? '';
        if (!Security::verifyCSRFToken($csrfToken)) {
            $_SESSION['register_error'] = 'Token keamanan tidak valid.';
            header('Location: /siswa-app/public/register');
            exit;
        }
        
        // Sanitize input
        $data = Security::sanitizeInput($_POST);
        
        // Create user
        $result = $this->userModel->createUser($data);
        
        if ($result['success']) {
            $_SESSION['register_success'] = 'User berhasil ditambahkan.';
        } else {
            $_SESSION['register_error'] = $result['message'];
        }
        
        header('Location: /siswa-app/public/register');
        exit;
    }
    
    /**
     * Show profile page
     */
    public function profile() {
        Security::requireAuth();
        
        $currentUser = $this->sessionManager->getCurrentUser();
        $userDetails = $this->userModel->getUserById($currentUser['id']);
        
        $data = [
            'title' => 'Profil',
            'user' => $userDetails,
            'csrf_token' => Security::generateCSRFToken(),
            'success' => $_SESSION['profile_success'] ?? null,
            'error' => $_SESSION['profile_error'] ?? null
        ];
        
        // Clear messages
        unset($_SESSION['profile_success'], $_SESSION['profile_error']);
        
        $this->view('auth/profile', $data);
    }
    
    /**
     * Update profile
     */
    public function updateProfile() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Location: /siswa-app/public/profile');
            exit;
        }
        
        Security::requireAuth();
        
        // Verify CSRF token
        $csrfToken = $_POST['csrf_token'] ?? '';
        if (!Security::verifyCSRFToken($csrfToken)) {
            $_SESSION['profile_error'] = 'Token keamanan tidak valid.';
            header('Location: /siswa-app/public/profile');
            exit;
        }
        
        $currentUser = $this->sessionManager->getCurrentUser();
        
        // Sanitize input
        $data = Security::sanitizeInput($_POST);
        
        // Remove fields that shouldn't be updated by regular users
        if ($currentUser['role'] !== 'admin') {
            unset($data['role'], $data['is_active']);
        }
        
        // Update user
        $result = $this->userModel->updateUser($currentUser['id'], $data);
        
        if ($result['success']) {
            $_SESSION['profile_success'] = 'Profil berhasil diupdate.';
            
            // Update session data if username changed
            if (!empty($data['username'])) {
                $_SESSION['username'] = $data['username'];
            }
            if (!empty($data['nama_lengkap'])) {
                $_SESSION['nama_lengkap'] = $data['nama_lengkap'];
            }
        } else {
            $_SESSION['profile_error'] = $result['message'];
        }
        
        header('Location: /siswa-app/public/profile');
        exit;
    }
    
    /**
     * Show user management page (admin only)
     */
    public function users() {
        Security::requireAuth();
        Security::requireRole('admin');
        
        $users = $this->userModel->getAllUsers();
        
        $data = [
            'title' => 'Manajemen User',
            'users' => $users,
            'csrf_token' => Security::generateCSRFToken(),
            'success' => $_SESSION['users_success'] ?? null,
            'error' => $_SESSION['users_error'] ?? null
        ];
        
        // Clear messages
        unset($_SESSION['users_success'], $_SESSION['users_error']);
        
        $this->view('auth/users', $data);
    }
    
    /**
     * Set remember me cookie
     */
    private function setRememberMeCookie($userId) {
        $token = Security::generateRandomString(64);
        $expires = time() + (30 * 24 * 60 * 60); // 30 days
        
        // Store token in database (you might want to create a remember_tokens table)
        // For now, we'll use a simple approach
        setcookie('remember_token', $token, $expires, '/', '', isset($_SERVER['HTTPS']), true);
        
        // You should store this token in database associated with user
        // and verify it on subsequent visits
    }
    
    /**
     * Clear remember me cookie
     */
    private function clearRememberMeCookie() {
        if (isset($_COOKIE['remember_token'])) {
            setcookie('remember_token', '', time() - 3600, '/');
        }
    }
    
    /**
     * Check remember me cookie
     */
    public function checkRememberMe() {
        if (isset($_COOKIE['remember_token']) && !$this->sessionManager->isValidSession()) {
            // Verify remember token and auto-login user
            // Implementation depends on your remember_tokens table structure
        }
    }
    
    /**
     * Show unauthorized page
     */
    public function unauthorized() {
        http_response_code(403);
        $data = ['title' => 'Akses Ditolak'];
        $this->view('auth/unauthorized', $data);
    }
    
    /**
     * Render view
     */
    private function view($path, $data = []) {
        extract($data);
        require_once __DIR__ . '/../views/layouts/header.php';
        require_once __DIR__ . '/../views/' . $path . '.php';
        require_once __DIR__ . '/../views/layouts/footer.php';
    }
}
