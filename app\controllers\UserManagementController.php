<?php
require_once __DIR__ . '/../models/User.php';
require_once __DIR__ . '/../helpers/Security.php';

class UserManagementController {
    private $userModel;
    
    public function __construct() {
        $this->userModel = new User();
    }
    
    /**
     * Show user management page (admin only)
     */
    public function index() {
        Security::requireAuth();
        Security::requireRole('admin');
        
        $users = $this->userModel->getAllUsers();
        
        $data = [
            'title' => 'Manajemen User',
            'users' => $users,
            'csrf_token' => Security::generateCSRFToken(),
            'success' => $_SESSION['user_mgmt_success'] ?? null,
            'error' => $_SESSION['user_mgmt_error'] ?? null
        ];
        
        // Clear messages
        unset($_SESSION['user_mgmt_success'], $_SESSION['user_mgmt_error']);
        
        $this->view('admin/user_management', $data);
    }
    
    /**
     * Show create user form
     */
    public function create() {
        Security::requireAuth();
        Security::requireRole('admin');
        
        $data = [
            'title' => 'Tambah User Baru',
            'csrf_token' => Security::generateCSRFToken(),
            'success' => $_SESSION['user_create_success'] ?? null,
            'error' => $_SESSION['user_create_error'] ?? null
        ];
        
        // Clear messages
        unset($_SESSION['user_create_success'], $_SESSION['user_create_error']);
        
        $this->view('admin/user_create', $data);
    }
    
    /**
     * Process create user
     */
    public function store() {
        Security::requireAuth();
        Security::requireRole('admin');
        
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Location: /siswa-app/public/admin/users/create');
            exit;
        }
        
        try {
            // Validate CSRF token
            if (!Security::validateCSRFToken($_POST['csrf_token'] ?? '')) {
                throw new Exception('Token keamanan tidak valid');
            }
            
            // Get form data
            $data = [
                'username' => Security::sanitizeInput($_POST['username'] ?? ''),
                'email' => Security::sanitizeInput($_POST['email'] ?? ''),
                'password' => $_POST['password'] ?? '',
                'role' => $_POST['role'] ?? 'staff',
                'nama_lengkap' => Security::sanitizeInput($_POST['nama_lengkap'] ?? ''),
                'pamong_type' => $_POST['pamong_type'] ?? null,
                'is_active' => isset($_POST['is_active']) ? 1 : 0
            ];
            
            // Validate required fields
            if (empty($data['username'])) {
                throw new Exception('Username harus diisi');
            }
            if (empty($data['email'])) {
                throw new Exception('Email harus diisi');
            }
            if (empty($data['password'])) {
                throw new Exception('Password harus diisi');
            }
            if (empty($data['nama_lengkap'])) {
                throw new Exception('Nama lengkap harus diisi');
            }
            
            // Validate email format
            if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
                throw new Exception('Format email tidak valid');
            }
            
            // Validate password length
            if (strlen($data['password']) < 6) {
                throw new Exception('Password minimal 6 karakter');
            }
            
            // Create user
            $result = $this->userModel->createUser($data);
            
            if ($result['success']) {
                // Log the action
                $this->logUserManagementAction('create', $result['user_id'], null, $data);
                
                $_SESSION['user_mgmt_success'] = 'User berhasil dibuat';
                header('Location: /siswa-app/public/admin/users');
            } else {
                throw new Exception($result['message']);
            }
            
        } catch (Exception $e) {
            $_SESSION['user_create_error'] = $e->getMessage();
            header('Location: /siswa-app/public/admin/users/create');
        }
        exit;
    }
    
    /**
     * Show edit user form
     */
    public function edit($id) {
        Security::requireAuth();
        Security::requireRole('admin');
        
        $user = $this->userModel->getUserById($id);
        if (!$user) {
            $_SESSION['user_mgmt_error'] = 'User tidak ditemukan';
            header('Location: /siswa-app/public/admin/users');
            exit;
        }
        
        $data = [
            'title' => 'Edit User - ' . $user['nama_lengkap'],
            'user' => $user,
            'csrf_token' => Security::generateCSRFToken(),
            'success' => $_SESSION['user_edit_success'] ?? null,
            'error' => $_SESSION['user_edit_error'] ?? null
        ];
        
        // Clear messages
        unset($_SESSION['user_edit_success'], $_SESSION['user_edit_error']);
        
        $this->view('admin/user_edit', $data);
    }
    
    /**
     * Process update user
     */
    public function update($id) {
        Security::requireAuth();
        Security::requireRole('admin');
        
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Location: /siswa-app/public/admin/users/edit/' . $id);
            exit;
        }
        
        try {
            // Validate CSRF token
            if (!Security::validateCSRFToken($_POST['csrf_token'] ?? '')) {
                throw new Exception('Token keamanan tidak valid');
            }
            
            $oldUser = $this->userModel->getUserById($id);
            if (!$oldUser) {
                throw new Exception('User tidak ditemukan');
            }
            
            // Get form data
            $data = [
                'username' => Security::sanitizeInput($_POST['username'] ?? ''),
                'email' => Security::sanitizeInput($_POST['email'] ?? ''),
                'role' => $_POST['role'] ?? 'staff',
                'nama_lengkap' => Security::sanitizeInput($_POST['nama_lengkap'] ?? ''),
                'pamong_type' => $_POST['pamong_type'] ?? null,
                'is_active' => isset($_POST['is_active']) ? 1 : 0
            ];
            
            // Add password if provided
            if (!empty($_POST['password'])) {
                if (strlen($_POST['password']) < 6) {
                    throw new Exception('Password minimal 6 karakter');
                }
                $data['password'] = $_POST['password'];
            }
            
            // Validate required fields
            if (empty($data['username'])) {
                throw new Exception('Username harus diisi');
            }
            if (empty($data['email'])) {
                throw new Exception('Email harus diisi');
            }
            if (empty($data['nama_lengkap'])) {
                throw new Exception('Nama lengkap harus diisi');
            }
            
            // Validate email format
            if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
                throw new Exception('Format email tidak valid');
            }
            
            // Update user
            $result = $this->userModel->updateUser($id, $data);
            
            if ($result['success']) {
                // Log the action
                $this->logUserManagementAction('update', $id, $oldUser, $data);
                
                $_SESSION['user_mgmt_success'] = 'User berhasil diupdate';
                header('Location: /siswa-app/public/admin/users');
            } else {
                throw new Exception($result['message']);
            }
            
        } catch (Exception $e) {
            $_SESSION['user_edit_error'] = $e->getMessage();
            header('Location: /siswa-app/public/admin/users/edit/' . $id);
        }
        exit;
    }
    
    /**
     * Reset user password
     */
    public function resetPassword($id) {
        Security::requireAuth();
        Security::requireRole('admin');
        
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Location: /siswa-app/public/admin/users');
            exit;
        }
        
        try {
            // Validate CSRF token
            if (!Security::validateCSRFToken($_POST['csrf_token'] ?? '')) {
                throw new Exception('Token keamanan tidak valid');
            }
            
            $user = $this->userModel->getUserById($id);
            if (!$user) {
                throw new Exception('User tidak ditemukan');
            }
            
            $newPassword = $_POST['new_password'] ?? '';
            if (empty($newPassword)) {
                throw new Exception('Password baru harus diisi');
            }
            if (strlen($newPassword) < 6) {
                throw new Exception('Password minimal 6 karakter');
            }
            
            // Update password
            $result = $this->userModel->updateUser($id, ['password' => $newPassword]);
            
            if ($result['success']) {
                // Log the action
                $this->logUserManagementAction('password_reset', $id, $user, ['password_reset' => true]);
                
                $_SESSION['user_mgmt_success'] = 'Password berhasil direset';
            } else {
                throw new Exception($result['message']);
            }
            
        } catch (Exception $e) {
            $_SESSION['user_mgmt_error'] = $e->getMessage();
        }
        
        header('Location: /siswa-app/public/admin/users');
        exit;
    }
    
    /**
     * Delete user
     */
    public function delete($id) {
        Security::requireAuth();
        Security::requireRole('admin');
        
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Location: /siswa-app/public/admin/users');
            exit;
        }
        
        try {
            // Validate CSRF token
            if (!Security::validateCSRFToken($_POST['csrf_token'] ?? '')) {
                throw new Exception('Token keamanan tidak valid');
            }
            
            $user = $this->userModel->getUserById($id);
            if (!$user) {
                throw new Exception('User tidak ditemukan');
            }
            
            // Prevent deleting admin user
            if ($user['role'] === 'admin' && $user['username'] === 'admin') {
                throw new Exception('User admin utama tidak dapat dihapus');
            }
            
            // Delete user
            $result = $this->userModel->deleteUser($id);
            
            if ($result['success']) {
                // Log the action
                $this->logUserManagementAction('delete', $id, $user, null);
                
                $_SESSION['user_mgmt_success'] = 'User berhasil dihapus';
            } else {
                throw new Exception($result['message']);
            }
            
        } catch (Exception $e) {
            $_SESSION['user_mgmt_error'] = $e->getMessage();
        }
        
        header('Location: /siswa-app/public/admin/users');
        exit;
    }
    
    /**
     * Log user management action
     */
    private function logUserManagementAction($action, $targetUserId, $oldValues, $newValues) {
        try {
            require_once __DIR__ . '/../models/Database.php';
            $db = new Database();
            
            $db->query("
                INSERT INTO user_management_log (admin_user_id, target_user_id, action_type, old_values, new_values, ip_address, user_agent)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ", [
                $_SESSION['user_id'],
                $targetUserId,
                $action,
                $oldValues ? json_encode($oldValues) : null,
                $newValues ? json_encode($newValues) : null,
                $_SERVER['REMOTE_ADDR'] ?? '',
                $_SERVER['HTTP_USER_AGENT'] ?? ''
            ]);
        } catch (Exception $e) {
            error_log("Error logging user management action: " . $e->getMessage());
        }
    }
    
    /**
     * Render view
     */
    private function view($path, $data = []) {
        extract($data);
        require_once __DIR__ . '/../views/layouts/header.php';
        require_once __DIR__ . '/../views/' . $path . '.php';
        require_once __DIR__ . '/../views/layouts/footer.php';
    }
}
