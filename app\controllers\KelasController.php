<?php
require_once __DIR__ . '/../models/Kelas.php';
require_once __DIR__ . '/../helpers/Security.php';

class KelasController {
    private $kelasModel;

    public function __construct() {
        $this->kelasModel = new Kelas();
    }

    public function index() {
        $data = [
            'title' => 'Daftar Kelas',
            'kelas' => $this->kelasModel->getAll(),
            'csrf_token' => Security::generateCSRFToken()
        ];
        $this->view('kelas/list', $data);
    }

    public function create() {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Verify CSRF token
            $csrfToken = $_POST['csrf_token'] ?? '';
            if (!Security::verifyCSRFToken($csrfToken)) {
                $_SESSION['error'] = 'Token keamanan tidak valid.';
                header('Location: /siswa-app/public/kelas/create');
                exit;
            }

            $nama_kelas = Security::sanitizeInput($_POST['nama_kelas'] ?? '');
            $tingkat = Security::sanitizeInput($_POST['tingkat'] ?? '');
            $jurusan = Security::sanitizeInput($_POST['jurusan'] ?? '');
            $tahun_pelajaran = Security::sanitizeInput($_POST['tahun_pelajaran'] ?? '');
            $wali_kelas = Security::sanitizeInput($_POST['wali_kelas'] ?? '');

            if ($nama_kelas && $tingkat && $tahun_pelajaran) {
                $data = [
                    'nama_kelas' => $nama_kelas,
                    'tingkat' => $tingkat,
                    'jurusan' => $jurusan,
                    'tahun_pelajaran' => $tahun_pelajaran,
                    'wali_kelas' => $wali_kelas,
                    'created_by' => $_SESSION['user_id'] ?? 1
                ];

                $result = $this->kelasModel->createKelas($data);
                if ($result) {
                    $_SESSION['success'] = 'Kelas berhasil ditambahkan.';
                    header('Location: /siswa-app/public/kelas');
                    exit;
                } else {
                    $_SESSION['error'] = 'Gagal menambahkan kelas.';
                }
            } else {
                $_SESSION['error'] = 'Nama kelas, tingkat, dan tahun pelajaran wajib diisi.';
            }
        }

        $data = [
            'title' => 'Tambah Kelas',
            'csrf_token' => Security::generateCSRFToken(),
            'success' => $_SESSION['success'] ?? null,
            'error' => $_SESSION['error'] ?? null
        ];

        // Clear messages
        unset($_SESSION['success'], $_SESSION['error']);

        $this->view('kelas/form', $data);
    }

    public function edit($id) {
        $kelas = $this->kelasModel->getById($id);
        if (!$kelas) {
            $_SESSION['error'] = 'Kelas tidak ditemukan.';
            header('Location: /siswa-app/public/kelas');
            exit;
        }

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Verify CSRF token
            $csrfToken = $_POST['csrf_token'] ?? '';
            if (!Security::verifyCSRFToken($csrfToken)) {
                $_SESSION['error'] = 'Token keamanan tidak valid.';
                header('Location: /siswa-app/public/kelas/edit/' . $id);
                exit;
            }

            $nama_kelas = Security::sanitizeInput($_POST['nama_kelas'] ?? '');
            $tingkat = Security::sanitizeInput($_POST['tingkat'] ?? '');
            $jurusan = Security::sanitizeInput($_POST['jurusan'] ?? '');
            $tahun_pelajaran = Security::sanitizeInput($_POST['tahun_pelajaran'] ?? '');
            $wali_kelas = Security::sanitizeInput($_POST['wali_kelas'] ?? '');

            if ($nama_kelas && $tingkat && $tahun_pelajaran) {
                $data = [
                    'nama_kelas' => $nama_kelas,
                    'tingkat' => $tingkat,
                    'jurusan' => $jurusan,
                    'tahun_pelajaran' => $tahun_pelajaran,
                    'wali_kelas' => $wali_kelas,
                    'updated_by' => $_SESSION['user_id'] ?? 1
                ];

                $result = $this->kelasModel->updateKelas($id, $data);
                if ($result) {
                    $_SESSION['success'] = 'Kelas berhasil diperbarui.';
                    header('Location: /siswa-app/public/kelas');
                    exit;
                } else {
                    $_SESSION['error'] = 'Gagal memperbarui kelas.';
                }
            } else {
                $_SESSION['error'] = 'Nama kelas, tingkat, dan tahun pelajaran wajib diisi.';
            }
        }

        $data = [
            'title' => 'Edit Kelas',
            'kelas' => $kelas,
            'csrf_token' => Security::generateCSRFToken(),
            'success' => $_SESSION['success'] ?? null,
            'error' => $_SESSION['error'] ?? null
        ];

        // Clear messages
        unset($_SESSION['success'], $_SESSION['error']);

        $this->view('kelas/form', $data);
    }

    public function detail($id) {
        $kelas = $this->kelasModel->getById($id);
        if (!$kelas) {
            $_SESSION['error'] = 'Kelas tidak ditemukan.';
            header('Location: /siswa-app/public/kelas');
            exit;
        }

        // Get students in this class
        require_once __DIR__ . '/../models/Siswa.php';
        $siswaModel = new Siswa();
        $siswaList = $siswaModel->getByKelasId($id);

        $data = [
            'title' => 'Detail Kelas - ' . $kelas['nama_kelas'],
            'kelas' => $kelas,
            'siswa_list' => $siswaList,
            'csrf_token' => Security::generateCSRFToken()
        ];

        $this->view('kelas/detail', $data);
    }

    public function delete($id) {
        Security::requireAuth();

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Verify CSRF token
            $csrfToken = $_POST['csrf_token'] ?? '';
            if (!Security::verifyCSRFToken($csrfToken)) {
                $_SESSION['error'] = 'Token keamanan tidak valid.';
                header('Location: /siswa-app/public/kelas');
                exit;
            }

            $result = $this->kelasModel->deleteKelas($id);
            if ($result) {
                $_SESSION['success'] = 'Kelas berhasil dihapus.';
            } else {
                $_SESSION['error'] = 'Gagal menghapus kelas.';
            }
        } else {
            // For GET request, redirect to delete confirmation
            $kelas = $this->kelasModel->getById($id);
            if (!$kelas) {
                $_SESSION['error'] = 'Kelas tidak ditemukan.';
                header('Location: /siswa-app/public/kelas');
                exit;
            }

            $result = $this->kelasModel->deleteKelas($id);
            if ($result) {
                $_SESSION['success'] = 'Kelas berhasil dihapus.';
            } else {
                $_SESSION['error'] = 'Gagal menghapus kelas.';
            }
        }

        header('Location: /siswa-app/public/kelas');
        exit;
    }

    private function view($path, $data = []) {
        extract($data);
        require_once __DIR__ . '/../views/layouts/header.php';
        require_once __DIR__ . '/../views/' . $path . '.php';
        require_once __DIR__ . '/../views/layouts/footer.php';
    }
}
?>