<?php
// Halaman khusus untuk tambah catatan siswa
?>

<div class="container-fluid py-4">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body bg-primary text-white rounded">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    <i class="bi bi-plus-circle-fill" style="font-size: 2.5rem;"></i>
                                </div>
                                <div>
                                    <h4 class="mb-1 fw-bold">Tambah Catatan Siswa</h4>
                                    <p class="mb-0 opacity-75">
                                        <i class="bi bi-person-fill me-1"></i>
                                        <?= htmlspecialchars($siswa['nama_lengkap']) ?>
                                        <span class="mx-2">•</span>
                                        <i class="bi bi-mortarboard me-1"></i>
                                        <?= htmlspecialchars($siswa['kelas'] ?? 'Kelas tidak diketahui') ?>
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 text-md-end">
                            <a href="/siswa-app/public/siswa/detail/<?= $siswa['id_siswa'] ?? $siswa['id'] ?>" 
                               class="btn btn-light btn-lg">
                                <i class="bi bi-arrow-left me-2"></i>
                                Kembali ke Detail
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Form Section -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0 text-dark">
                        <i class="bi bi-pencil-square me-2"></i>
                        Form Tambah Catatan
                    </h5>
                </div>
                <div class="card-body p-4">
                    <form method="POST" action="/siswa-app/public/catatan/create" id="addCatatanForm" novalidate>
                        <input type="hidden" name="csrf_token" value="<?= $csrf_token ?>">
                        <input type="hidden" name="siswa_id" value="<?= $siswa['id_siswa'] ?? $siswa['id'] ?>">

                        <!-- Alert Container -->
                        <div id="alertContainer"></div>

                        <!-- Basic Information Row -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="jenis_catatan" class="form-label fw-semibold">
                                        <i class="bi bi-tag me-1"></i>
                                        Jenis Catatan <span class="text-danger">*</span>
                                    </label>
                                    <select class="form-select form-select-lg" id="jenis_catatan" name="jenis_catatan" required>
                                        <option value="">Pilih Jenis Catatan</option>
                                        <?php if (isset($catatan_categories)): ?>
                                            <optgroup label="📚 Pamong">
                                                <?php foreach ($catatan_categories['pamong'] as $cat): ?>
                                                    <option value="<?= $cat['kode_kategori'] ?>">
                                                        <?= $cat['nama_kategori'] ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </optgroup>
                                            <optgroup label="👨‍🏫 Wali Kelas">
                                                <?php foreach ($catatan_categories['wali_kelas'] as $cat): ?>
                                                    <option value="<?= $cat['kode_kategori'] ?>">
                                                        <?= $cat['nama_kategori'] ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </optgroup>
                                            <optgroup label="🧠 Bimbingan Konseling">
                                                <?php foreach ($catatan_categories['bk'] as $cat): ?>
                                                    <option value="<?= $cat['kode_kategori'] ?>">
                                                        <?= $cat['nama_kategori'] ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </optgroup>
                                        <?php endif; ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="tanggal_catatan" class="form-label fw-semibold">
                                        <i class="bi bi-calendar me-1"></i>
                                        Tanggal <span class="text-danger">*</span>
                                    </label>
                                    <input type="date" 
                                           class="form-control form-control-lg" 
                                           id="tanggal_catatan" 
                                           name="tanggal_catatan"
                                           value="<?= date('Y-m-d') ?>" 
                                           required>
                                </div>
                            </div>
                        </div>

                        <!-- Priority and Status Row -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="tingkat_prioritas" class="form-label fw-semibold">
                                        <i class="bi bi-exclamation-triangle me-1"></i>
                                        Tingkat Prioritas
                                    </label>
                                    <select class="form-select form-select-lg" id="tingkat_prioritas" name="tingkat_prioritas">
                                        <option value="rendah">🟢 Rendah</option>
                                        <option value="sedang" selected>🔵 Sedang</option>
                                        <option value="tinggi">🟡 Tinggi</option>
                                        <option value="urgent">🔴 Urgent</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="status_catatan" class="form-label fw-semibold">
                                        <i class="bi bi-check-circle me-1"></i>
                                        Status Catatan
                                    </label>
                                    <select class="form-select form-select-lg" id="status_catatan" name="status_catatan">
                                        <option value="draft">📝 Draft</option>
                                        <option value="aktif" selected>✅ Aktif</option>
                                        <option value="selesai">✔️ Selesai</option>
                                        <option value="ditunda">⏸️ Ditunda</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Title Section -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="form-group">
                                    <label for="judul_catatan" class="form-label fw-semibold">
                                        <i class="bi bi-card-heading me-1"></i>
                                        Judul Catatan <span class="text-danger">*</span>
                                    </label>
                                    <input type="text" 
                                           class="form-control form-control-lg" 
                                           id="judul_catatan" 
                                           name="judul_catatan" 
                                           placeholder="Masukkan judul catatan yang jelas dan deskriptif..."
                                           required>
                                    <div class="form-text">
                                        <i class="bi bi-info-circle me-1"></i>
                                        Gunakan judul yang singkat namun informatif
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Content Section -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="form-group">
                                    <label for="isi_catatan" class="form-label fw-semibold">
                                        <i class="bi bi-file-text me-1"></i>
                                        Isi Catatan <span class="text-danger">*</span>
                                    </label>
                                    <textarea class="form-control" 
                                              id="isi_catatan" 
                                              name="isi_catatan" 
                                              rows="8" 
                                              placeholder="Tuliskan detail catatan di sini...&#10;&#10;Contoh:&#10;- Deskripsi kejadian/situasi&#10;- Tindakan yang telah dilakukan&#10;- Hasil observasi&#10;- Rekomendasi"
                                              required></textarea>
                                    <div class="form-text">
                                        <i class="bi bi-info-circle me-1"></i>
                                        Jelaskan secara detail dan objektif
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Follow-up Section -->
                        <div class="row mb-4">
                            <div class="col-md-8">
                                <div class="form-group">
                                    <label for="tindak_lanjut" class="form-label fw-semibold">
                                        <i class="bi bi-arrow-right-circle me-1"></i>
                                        Rencana Tindak Lanjut
                                    </label>
                                    <textarea class="form-control" 
                                              id="tindak_lanjut" 
                                              name="tindak_lanjut" 
                                              rows="4" 
                                              placeholder="Rencana tindak lanjut (opsional)...&#10;&#10;Contoh:&#10;- Monitoring berkala&#10;- Konsultasi dengan orang tua&#10;- Program khusus"></textarea>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="tanggal_tindak_lanjut" class="form-label fw-semibold">
                                        <i class="bi bi-calendar-event me-1"></i>
                                        Target Tanggal
                                    </label>
                                    <input type="date" 
                                           class="form-control form-control-lg" 
                                           id="tanggal_tindak_lanjut" 
                                           name="tanggal_tindak_lanjut">
                                    <div class="form-text">
                                        <small class="text-muted">Tanggal target tindak lanjut</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-between align-items-center pt-3 border-top">
                                    <div>
                                        <a href="/siswa-app/public/siswa/detail/<?= $siswa['id_siswa'] ?? $siswa['id'] ?>" 
                                           class="btn btn-outline-secondary btn-lg">
                                            <i class="bi bi-x-circle me-2"></i>
                                            Batal
                                        </a>
                                    </div>
                                    <div>
                                        <button type="submit" class="btn btn-primary btn-lg px-5" id="submitBtn">
                                            <i class="bi bi-save me-2"></i>
                                            Simpan Catatan
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Custom styling for add catatan page */
.form-control, .form-select {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.15);
    transform: translateY(-1px);
}

.form-control-lg, .form-select-lg {
    padding: 0.75rem 1rem;
    font-size: 1.1rem;
}

.form-label {
    color: #495057;
    margin-bottom: 0.75rem;
}

.form-label i {
    color: #007bff;
}

.card {
    border-radius: 12px;
    overflow: hidden;
}

.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.btn-lg {
    padding: 0.75rem 2rem;
}

/* Form group spacing */
.form-group {
    margin-bottom: 1.5rem;
}

/* Textarea styling */
textarea.form-control {
    resize: vertical;
    min-height: 120px;
}

/* Priority and status select styling */
#tingkat_prioritas option,
#status_catatan option {
    padding: 0.5rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .container-fluid {
        padding: 1rem;
    }
    
    .card-body {
        padding: 1.5rem !important;
    }
    
    .btn-lg {
        padding: 0.5rem 1.5rem;
        font-size: 1rem;
    }
    
    .form-control-lg, .form-select-lg {
        padding: 0.5rem 0.75rem;
        font-size: 1rem;
    }
}

/* Loading state */
.btn.loading {
    pointer-events: none;
    opacity: 0.7;
}

.btn.loading i {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
</style>

<script>
// Form validation and enhancement
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('addCatatanForm');
    const submitBtn = document.getElementById('submitBtn');
    
    // Form validation
    function validateForm() {
        let isValid = true;
        const alertContainer = document.getElementById('alertContainer');
        alertContainer.innerHTML = '';

        // Clear previous validation states
        form.querySelectorAll('.is-invalid').forEach(el => el.classList.remove('is-invalid'));
        form.querySelectorAll('.invalid-feedback').forEach(el => el.remove());

        // Validate jenis catatan
        const jenisCatatan = document.getElementById('jenis_catatan');
        if (!jenisCatatan.value) {
            showFieldError(jenisCatatan, 'Jenis catatan harus dipilih');
            isValid = false;
        }

        // Validate judul
        const judul = document.getElementById('judul_catatan');
        if (!judul.value.trim()) {
            showFieldError(judul, 'Judul catatan harus diisi');
            isValid = false;
        } else if (judul.value.length > 255) {
            showFieldError(judul, 'Judul catatan maksimal 255 karakter');
            isValid = false;
        }

        // Validate isi catatan
        const isi = document.getElementById('isi_catatan');
        if (!isi.value.trim()) {
            showFieldError(isi, 'Isi catatan harus diisi');
            isValid = false;
        }

        // Validate tanggal
        const tanggal = document.getElementById('tanggal_catatan');
        if (!tanggal.value) {
            showFieldError(tanggal, 'Tanggal catatan harus diisi');
            isValid = false;
        }

        // Validate tanggal tindak lanjut (if provided)
        const tanggalTindakLanjut = document.getElementById('tanggal_tindak_lanjut');
        if (tanggalTindakLanjut.value && tanggal.value) {
            if (new Date(tanggalTindakLanjut.value) < new Date(tanggal.value)) {
                showFieldError(tanggalTindakLanjut, 'Tanggal tindak lanjut tidak boleh sebelum tanggal catatan');
                isValid = false;
            }
        }

        return isValid;
    }

    function showFieldError(field, message) {
        field.classList.add('is-invalid');
        const feedback = document.createElement('div');
        feedback.className = 'invalid-feedback';
        feedback.textContent = message;
        field.parentNode.appendChild(feedback);
    }

    function showAlert(type, message) {
        const alertContainer = document.getElementById('alertContainer');
        const alert = document.createElement('div');
        alert.className = `alert alert-${type} alert-dismissible fade show`;
        alert.innerHTML = `
            <i class="bi bi-${type === 'danger' ? 'exclamation-triangle' : 'check-circle'} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        alertContainer.appendChild(alert);
    }

    // Form submission handling
    form.addEventListener('submit', function(e) {
        e.preventDefault();

        if (!validateForm()) {
            showAlert('danger', 'Mohon perbaiki kesalahan pada form');
            return;
        }

        // Show loading state
        submitBtn.classList.add('loading');
        submitBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>Menyimpan...';
        submitBtn.disabled = true;

        // Submit form
        this.submit();
    });
    
    // Auto-resize textarea
    const textareas = document.querySelectorAll('textarea');
    textareas.forEach(textarea => {
        textarea.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = (this.scrollHeight) + 'px';
        });
    });
    
    // Character counter for title
    const judulInput = document.getElementById('judul_catatan');
    const judulCounter = document.createElement('div');
    judulCounter.className = 'form-text text-end';
    judulCounter.innerHTML = '<small class="text-muted">0/255 karakter</small>';
    judulInput.parentNode.appendChild(judulCounter);
    
    judulInput.addEventListener('input', function() {
        const length = this.value.length;
        judulCounter.innerHTML = `<small class="text-muted">${length}/255 karakter</small>`;
        
        if (length > 200) {
            judulCounter.innerHTML = `<small class="text-warning">${length}/255 karakter</small>`;
        }
        if (length > 240) {
            judulCounter.innerHTML = `<small class="text-danger">${length}/255 karakter</small>`;
        }
    });
});
</script>
