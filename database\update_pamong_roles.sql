-- Update database untuk implementasi Role <PERSON><PERSON> dan Staff
-- File: database/update_pamong_roles.sql

-- 1. Update enum role untuk menambah pamong roles
ALTER TABLE users MODIFY COLUMN role ENUM(
    'admin', 
    'pamong_mp',    -- <PERSON><PERSON> (KPP)
    'pamong_mt',    -- <PERSON><PERSON> MT (X) 
    'pamong_mm',    -- <PERSON><PERSON> MM (XI)
    'pamong_mu',    -- <PERSON><PERSON> MU (XII, KPA)
    'wali_kelas', 
    'staff'
) DEFAULT 'staff';

-- 2. Tambah kolom pamong_type untuk spesifikasi pamong
ALTER TABLE users ADD COLUMN pamong_type ENUM('mp', 'mt', 'mm', 'mu') NULL AFTER role;

-- 3. Tambah kolom tingkat_akses untuk mapping akses tingkat
ALTER TABLE users ADD COLUMN tingkat_akses JSON NULL AFTER pamong_type;

-- 4. Update existing guru users menjadi pamong
UPDATE users SET role = 'pamong_mp' WHERE role = 'guru' AND nama_lengkap LIKE '%MP%';
UPDATE users SET role = 'pamong_mt' WHERE role = 'guru' AND nama_lengkap LIKE '%MT%';
UPDATE users SET role = 'pamong_mm' WHERE role = 'guru' AND nama_lengkap LIKE '%MM%';
UPDATE users SET role = 'pamong_mu' WHERE role = 'guru' AND nama_lengkap LIKE '%MU%';

-- 5. Insert sample pamong users
INSERT INTO users (username, email, password, role, pamong_type, nama_lengkap, tingkat_akses, is_active) VALUES 
('pamong_mp', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'pamong_mp', 'mp', 'Pamong MP - Budi Hartono', '["KPP"]', TRUE),
('pamong_mt', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'pamong_mt', 'mt', 'Pamong MT - Sari Dewi', '["X"]', TRUE),
('pamong_mm', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'pamong_mm', 'mm', 'Pamong MM - Ahmad Yusuf', '["XI"]', TRUE),
('pamong_mu', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'pamong_mu', 'mu', 'Pamong MU - Rina Sari', '["XII", "KPA"]', TRUE);
-- Password untuk semua: password

-- 6. Insert sample staff user
INSERT INTO users (username, email, password, role, nama_lengkap, is_active) VALUES 
('staff_tata_usaha', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'staff', 'Staff Tata Usaha - Indah Permata', TRUE);

-- 7. Update kategori catatan allowed_roles untuk pamong
UPDATE kategori_catatan SET allowed_roles = '["admin", "pamong_mp", "pamong_mt", "pamong_mm", "pamong_mu"]' WHERE kode_kategori LIKE 'pamong_%';

-- 8. Tambah beberapa kelas sample untuk testing
INSERT IGNORE INTO kelas (nama_kelas, tingkat, jurusan, tahun_pelajaran, created_by) VALUES 
('KPP-A', 'KPP', 'Umum', '2024/2025', 1),
('KPP-B', 'KPP', 'Umum', '2024/2025', 1),
('X-IPA-2', 'X', 'IPA', '2024/2025', 1),
('X-IPS-2', 'X', 'IPS', '2024/2025', 1),
('XI-IPA-2', 'XI', 'IPA', '2024/2025', 1),
('XI-IPS-1', 'XI', 'IPS', '2024/2025', 1),
('XII-IPA-1', 'XII', 'IPA', '2024/2025', 1),
('XII-IPS-1', 'XII', 'IPS', '2024/2025', 1),
('KPA-A', 'KPA', 'Umum', '2024/2025', 1),
('KPA-B', 'KPA', 'Umum', '2024/2025', 1);

-- 9. Tambah sample siswa untuk testing pamong access
INSERT IGNORE INTO siswa (nis, nama_lengkap, jenis_kelamin, kelas_id, tahun_masuk, status_siswa, created_by) VALUES
-- KPP students
('2024101', 'Andi Pratama', 'L', (SELECT id FROM kelas WHERE nama_kelas = 'KPP-A' LIMIT 1), 2024, 'aktif', 1),
('2024102', 'Siti Aisyah', 'P', (SELECT id FROM kelas WHERE nama_kelas = 'KPP-A' LIMIT 1), 2024, 'aktif', 1),
('2024103', 'Budi Santoso', 'L', (SELECT id FROM kelas WHERE nama_kelas = 'KPP-B' LIMIT 1), 2024, 'aktif', 1),

-- X students  
('2024201', 'Dewi Lestari', 'P', (SELECT id FROM kelas WHERE nama_kelas = 'X-IPA-2' LIMIT 1), 2024, 'aktif', 1),
('2024202', 'Rizki Maulana', 'L', (SELECT id FROM kelas WHERE nama_kelas = 'X-IPS-2' LIMIT 1), 2024, 'aktif', 1),

-- XI students
('2024301', 'Fitri Handayani', 'P', (SELECT id FROM kelas WHERE nama_kelas = 'XI-IPA-2' LIMIT 1), 2024, 'aktif', 1),
('2024302', 'Agus Setiawan', 'L', (SELECT id FROM kelas WHERE nama_kelas = 'XI-IPS-1' LIMIT 1), 2024, 'aktif', 1),

-- XII students
('2024401', 'Maya Sari', 'P', (SELECT id FROM kelas WHERE nama_kelas = 'XII-IPA-1' LIMIT 1), 2024, 'aktif', 1),
('2024402', 'Doni Prasetyo', 'L', (SELECT id FROM kelas WHERE nama_kelas = 'XII-IPS-1' LIMIT 1), 2024, 'aktif', 1),

-- KPA students
('2024501', 'Lina Marlina', 'P', (SELECT id FROM kelas WHERE nama_kelas = 'KPA-A' LIMIT 1), 2024, 'aktif', 1),
('2024502', 'Eko Wijaya', 'L', (SELECT id FROM kelas WHERE nama_kelas = 'KPA-B' LIMIT 1), 2024, 'aktif', 1);

-- 10. Tambah view untuk pamong access
CREATE OR REPLACE VIEW view_siswa_by_tingkat AS
SELECT 
    s.*,
    k.nama_kelas,
    k.tingkat,
    k.jurusan,
    k.tahun_pelajaran
FROM siswa s
JOIN kelas k ON s.kelas_id = k.id
WHERE s.status_siswa = 'aktif' AND k.is_active = 1;

-- 11. Tambah stored function untuk cek akses pamong
DELIMITER //
CREATE OR REPLACE FUNCTION can_pamong_access_siswa(
    p_user_id INT,
    p_siswa_id INT
) RETURNS BOOLEAN
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE v_user_role VARCHAR(20);
    DECLARE v_pamong_type VARCHAR(10);
    DECLARE v_siswa_tingkat VARCHAR(10);
    DECLARE v_count INT DEFAULT 0;
    
    -- Get user role and pamong type
    SELECT role, pamong_type INTO v_user_role, v_pamong_type
    FROM users WHERE id = p_user_id;
    
    -- Get siswa tingkat
    SELECT k.tingkat INTO v_siswa_tingkat
    FROM siswa s
    JOIN kelas k ON s.kelas_id = k.id
    WHERE s.id_siswa = p_siswa_id;
    
    -- Check access based on pamong type
    CASE v_user_role
        WHEN 'pamong_mp' THEN
            RETURN v_siswa_tingkat = 'KPP';
        WHEN 'pamong_mt' THEN
            RETURN v_siswa_tingkat = 'X';
        WHEN 'pamong_mm' THEN
            RETURN v_siswa_tingkat = 'XI';
        WHEN 'pamong_mu' THEN
            RETURN v_siswa_tingkat IN ('XII', 'KPA');
        WHEN 'admin' THEN
            RETURN TRUE;
        ELSE
            RETURN FALSE;
    END CASE;
END //
DELIMITER ;

-- 12. Tambah stored procedure untuk get siswa by pamong
DELIMITER //
CREATE OR REPLACE PROCEDURE get_siswa_by_pamong(
    IN p_user_id INT
)
BEGIN
    DECLARE v_user_role VARCHAR(20);
    DECLARE v_pamong_type VARCHAR(10);
    
    -- Get user role and pamong type
    SELECT role, pamong_type INTO v_user_role, v_pamong_type
    FROM users WHERE id = p_user_id;
    
    -- Return siswa based on pamong type
    CASE v_user_role
        WHEN 'pamong_mp' THEN
            SELECT s.*, k.nama_kelas, k.tingkat, k.jurusan
            FROM siswa s
            JOIN kelas k ON s.kelas_id = k.id
            WHERE k.tingkat = 'KPP' AND s.status_siswa = 'aktif' AND k.is_active = 1
            ORDER BY s.nama_lengkap;
        WHEN 'pamong_mt' THEN
            SELECT s.*, k.nama_kelas, k.tingkat, k.jurusan
            FROM siswa s
            JOIN kelas k ON s.kelas_id = k.id
            WHERE k.tingkat = 'X' AND s.status_siswa = 'aktif' AND k.is_active = 1
            ORDER BY s.nama_lengkap;
        WHEN 'pamong_mm' THEN
            SELECT s.*, k.nama_kelas, k.tingkat, k.jurusan
            FROM siswa s
            JOIN kelas k ON s.kelas_id = k.id
            WHERE k.tingkat = 'XI' AND s.status_siswa = 'aktif' AND k.is_active = 1
            ORDER BY s.nama_lengkap;
        WHEN 'pamong_mu' THEN
            SELECT s.*, k.nama_kelas, k.tingkat, k.jurusan
            FROM siswa s
            JOIN kelas k ON s.kelas_id = k.id
            WHERE k.tingkat IN ('XII', 'KPA') AND s.status_siswa = 'aktif' AND k.is_active = 1
            ORDER BY s.nama_lengkap;
        ELSE
            SELECT s.*, k.nama_kelas, k.tingkat, k.jurusan
            FROM siswa s
            JOIN kelas k ON s.kelas_id = k.id
            WHERE 1=0; -- Return empty result
    END CASE;
END //
DELIMITER ;

-- 13. Tambah indexes untuk performance
CREATE INDEX idx_users_role_pamong ON users(role, pamong_type);
CREATE INDEX idx_kelas_tingkat_active ON kelas(tingkat, is_active);
CREATE INDEX idx_siswa_status_kelas ON siswa(status_siswa, kelas_id);

-- 14. Tambah tabel untuk user management log
CREATE TABLE IF NOT EXISTS user_management_log (
    id INT PRIMARY KEY AUTO_INCREMENT,
    admin_user_id INT NOT NULL,
    target_user_id INT NOT NULL,
    action_type ENUM('create', 'update', 'delete', 'password_reset') NOT NULL,
    old_values JSON NULL,
    new_values JSON NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (admin_user_id) REFERENCES users(id),
    FOREIGN KEY (target_user_id) REFERENCES users(id),
    INDEX idx_admin_user (admin_user_id),
    INDEX idx_target_user (target_user_id),
    INDEX idx_action_type (action_type),
    INDEX idx_created_at (created_at)
);

-- 15. Verification queries
-- SELECT 'Database update completed successfully' as status;
-- SELECT 'Pamong users created:' as info;
-- SELECT username, nama_lengkap, role, pamong_type FROM users WHERE role LIKE 'pamong_%';
-- SELECT 'Staff users:' as info;
-- SELECT username, nama_lengkap, role FROM users WHERE role = 'staff';
-- SELECT 'Sample siswa by tingkat:' as info;
-- SELECT k.tingkat, COUNT(*) as jumlah_siswa FROM siswa s JOIN kelas k ON s.kelas_id = k.id GROUP BY k.tingkat;
