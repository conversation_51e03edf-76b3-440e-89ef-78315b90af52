<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="card-title mb-0">
                        <i class="bi bi-building"></i> Daftar Kelas
                    </h4>
                    <a href="/siswa-app/public/kelas/create" class="btn btn-primary">
                        <i class="bi bi-plus-circle"></i> Tambah Kelas
                    </a>
                </div>
                <div class="card-body">
                    <?php if (empty($kelas)): ?>
                        <div class="alert alert-info text-center">
                            <i class="bi bi-info-circle"></i>
                            Belum ada data kelas. <a href="/siswa-app/public/kelas/create">Tambah kelas pertama</a>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>ID</th>
                                        <th><PERSON><PERSON></th>
                                        <th>Tingkat</th>
                                        <th><PERSON><PERSON><PERSON></th>
                                        <th><PERSON><PERSON></th>
                                        <th>Wali Kelas</th>
                                        <th>Aksi</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($kelas as $k): ?>
                                    <tr>
                                        <td><?= htmlspecialchars($k['id_kelas'] ?? '') ?></td>
                                        <td>
                                            <strong><?= htmlspecialchars($k['nama_kelas'] ?? '') ?></strong>
                                        </td>
                                        <td>
                                            <?php
                                            $tingkat = $k['tingkat'] ?? '';

                                            // Determine badge class
                                            switch($tingkat) {
                                                case 'KPP':
                                                    $badgeClass = 'bg-info';
                                                    $tingkatLabel = 'KPP';
                                                    break;
                                                case 'X':
                                                    $badgeClass = 'bg-success';
                                                    $tingkatLabel = 'Kelas X';
                                                    break;
                                                case 'XI':
                                                    $badgeClass = 'bg-warning';
                                                    $tingkatLabel = 'Kelas XI';
                                                    break;
                                                case 'XII':
                                                    $badgeClass = 'bg-danger';
                                                    $tingkatLabel = 'Kelas XII';
                                                    break;
                                                case 'KPA':
                                                    $badgeClass = 'bg-purple';
                                                    $tingkatLabel = 'KPA';
                                                    break;
                                                default:
                                                    $badgeClass = 'bg-secondary';
                                                    $tingkatLabel = $tingkat;
                                            }
                                            ?>
                                            <span class="badge <?= $badgeClass ?>"><?= htmlspecialchars($tingkatLabel) ?></span>
                                        </td>
                                        <td><?= htmlspecialchars($k['jurusan'] ?? '-') ?></td>
                                        <td><?= htmlspecialchars($k['tahun_pelajaran'] ?? '-') ?></td>
                                        <td><?= htmlspecialchars($k['wali_kelas'] ?? '-') ?></td>
                                        <td>
                                            <div class="btn-group btn-group-sm" role="group">
                                                <a href="/siswa-app/public/kelas/edit/<?= $k['id_kelas'] ?>"
                                                   class="btn btn-outline-warning" title="Edit">
                                                    <i class="bi bi-pencil"></i>
                                                </a>
                                                <a href="/siswa-app/public/kelas/detail/<?= $k['id_kelas'] ?>"
                                                   class="btn btn-outline-info" title="Detail">
                                                    <i class="bi bi-eye"></i>
                                                </a>
                                                <button type="button" class="btn btn-outline-danger"
                                                        onclick="confirmDelete(<?= $k['id_kelas'] ?>)" title="Hapus">
                                                    <i class="bi bi-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.bg-purple {
    background-color: #6f42c1 !important;
}
</style>

<script>
function confirmDelete(id) {
    if (confirm('Apakah Anda yakin ingin menghapus kelas ini?')) {
        window.location.href = '/siswa-app/public/kelas/delete/' + id;
    }
}
</script>