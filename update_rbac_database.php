<?php
// Script untuk update database RBAC Wali Kelas
// File: update_rbac_database.php

require_once __DIR__ . '/app/models/Database.php';

try {
    echo "<h2>🔄 Updating Database for RBAC Wali Kelas...</h2>";
    
    $db = new Database();
    
    // 1. Update enum role untuk menambah wali_kelas
    echo "<p>1. Updating user roles enum...</p>";
    $db->query("ALTER TABLE users MODIFY COLUMN role ENUM('admin', 'guru', 'staff', 'wali_kelas') DEFAULT 'staff'");
    echo "<p style='color: green;'>✅ User roles updated</p>";
    
    // 2. Tambah kolom wali_kelas_id ke tabel kelas
    echo "<p>2. Adding wali_kelas_id column to kelas table...</p>";
    try {
        $db->query("ALTER TABLE kelas ADD COLUMN wali_kelas_id INT NULL AFTER wali_kelas");
        echo "<p style='color: green;'>✅ Column wali_kelas_id added</p>";
    } catch (Exception $e) {
        if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
            echo "<p style='color: orange;'>⚠️ Column wali_kelas_id already exists</p>";
        } else {
            throw $e;
        }
    }
    
    // 3. Tambah foreign key constraint
    echo "<p>3. Adding foreign key constraint...</p>";
    try {
        $db->query("ALTER TABLE kelas ADD CONSTRAINT fk_kelas_wali_kelas FOREIGN KEY (wali_kelas_id) REFERENCES users(id) ON DELETE SET NULL");
        echo "<p style='color: green;'>✅ Foreign key constraint added</p>";
    } catch (Exception $e) {
        if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
            echo "<p style='color: orange;'>⚠️ Foreign key constraint already exists</p>";
        } else {
            throw $e;
        }
    }
    
    // 4. Tambah index untuk performance
    echo "<p>4. Adding index for performance...</p>";
    try {
        $db->query("ALTER TABLE kelas ADD INDEX idx_wali_kelas_id (wali_kelas_id)");
        echo "<p style='color: green;'>✅ Index added</p>";
    } catch (Exception $e) {
        if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
            echo "<p style='color: orange;'>⚠️ Index already exists</p>";
        } else {
            throw $e;
        }
    }
    
    // 5. Insert sample wali kelas users
    echo "<p>5. Creating sample wali kelas users...</p>";
    $waliKelasUsers = [
        ['wali_x_ipa1', '<EMAIL>', 'Budi Santoso'],
        ['wali_x_ips1', '<EMAIL>', 'Siti Aminah'],
        ['wali_xi_ipa1', '<EMAIL>', 'Ahmad Rahman']
    ];
    
    foreach ($waliKelasUsers as $user) {
        try {
            $db->query(
                "INSERT INTO users (username, email, password, role, nama_lengkap, is_active) VALUES (?, ?, ?, 'wali_kelas', ?, TRUE)",
                [$user[0], $user[1], '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', $user[2]]
            );
            echo "<p style='color: green;'>✅ Created user: {$user[2]} ({$user[0]})</p>";
        } catch (Exception $e) {
            if (strpos($e->getMessage(), 'Duplicate entry') !== false) {
                echo "<p style='color: orange;'>⚠️ User {$user[0]} already exists</p>";
            } else {
                throw $e;
            }
        }
    }
    
    // 6. Update kelas dengan wali_kelas_id yang sesuai
    echo "<p>6. Linking kelas with wali_kelas_id...</p>";
    $kelasUpdates = [
        ['X-IPA-1', 'wali_x_ipa1'],
        ['X-IPS-1', 'wali_x_ips1'],
        ['XI-IPA-1', 'wali_xi_ipa1']
    ];
    
    foreach ($kelasUpdates as $update) {
        $user = $db->fetch("SELECT id FROM users WHERE username = ?", [$update[1]]);
        if ($user) {
            $db->query("UPDATE kelas SET wali_kelas_id = ? WHERE nama_kelas = ?", [$user['id'], $update[0]]);
            echo "<p style='color: green;'>✅ Linked {$update[0]} with {$update[1]}</p>";
        }
    }
    
    // 7. Tambah tabel user_kelas_mapping
    echo "<p>7. Creating user_kelas_mapping table...</p>";
    try {
        $db->query("
            CREATE TABLE IF NOT EXISTS user_kelas_mapping (
                id INT PRIMARY KEY AUTO_INCREMENT,
                user_id INT NOT NULL,
                kelas_id INT NOT NULL,
                role_type ENUM('wali_kelas', 'guru_mapel', 'guru_piket') DEFAULT 'wali_kelas',
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                FOREIGN KEY (kelas_id) REFERENCES kelas(id) ON DELETE CASCADE,
                UNIQUE KEY unique_user_kelas_role (user_id, kelas_id, role_type),
                INDEX idx_user_id (user_id),
                INDEX idx_kelas_id (kelas_id),
                INDEX idx_role_type (role_type)
            )
        ");
        echo "<p style='color: green;'>✅ Table user_kelas_mapping created</p>";
    } catch (Exception $e) {
        echo "<p style='color: orange;'>⚠️ Table user_kelas_mapping might already exist</p>";
    }
    
    // 8. Insert mapping untuk wali kelas yang sudah ada
    echo "<p>8. Creating user-kelas mappings...</p>";
    $db->query("
        INSERT IGNORE INTO user_kelas_mapping (user_id, kelas_id, role_type)
        SELECT wali_kelas_id, id, 'wali_kelas'
        FROM kelas 
        WHERE wali_kelas_id IS NOT NULL
    ");
    echo "<p style='color: green;'>✅ User-kelas mappings created</p>";
    
    // 9. Update kategori catatan dengan allowed roles
    echo "<p>9. Updating kategori catatan with allowed roles...</p>";
    try {
        $db->query("ALTER TABLE kategori_catatan ADD COLUMN allowed_roles JSON DEFAULT NULL AFTER is_active");
        echo "<p style='color: green;'>✅ Column allowed_roles added to kategori_catatan</p>";
    } catch (Exception $e) {
        if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
            echo "<p style='color: orange;'>⚠️ Column allowed_roles already exists</p>";
        } else {
            throw $e;
        }
    }
    
    // Update allowed roles
    $db->query("UPDATE kategori_catatan SET allowed_roles = '[\"admin\", \"guru\"]' WHERE kode_kategori LIKE 'pamong_%'");
    $db->query("UPDATE kategori_catatan SET allowed_roles = '[\"admin\", \"wali_kelas\"]' WHERE kode_kategori LIKE 'wali_%'");
    $db->query("UPDATE kategori_catatan SET allowed_roles = '[\"admin\", \"guru\"]' WHERE kode_kategori LIKE 'bk_%'");
    echo "<p style='color: green;'>✅ Allowed roles updated for kategori catatan</p>";
    
    // 10. Tambah beberapa sample siswa untuk testing
    echo "<p>10. Adding sample students for testing...</p>";
    $sampleSiswa = [
        ['2024001', 'Ahmad Fauzi', 'L', 1],
        ['2024002', 'Siti Nurhaliza', 'P', 1],
        ['2024003', 'Budi Prasetyo', 'L', 2],
        ['2024004', 'Dewi Sartika', 'P', 2],
        ['2024005', 'Rizki Ramadhan', 'L', 3]
    ];
    
    foreach ($sampleSiswa as $siswa) {
        try {
            $db->query(
                "INSERT INTO siswa (nis, nama_lengkap, jenis_kelamin, kelas_id, tahun_masuk, status_siswa, created_by) VALUES (?, ?, ?, ?, 2024, 'aktif', 1)",
                $siswa
            );
            echo "<p style='color: green;'>✅ Added student: {$siswa[1]} ({$siswa[0]})</p>";
        } catch (Exception $e) {
            if (strpos($e->getMessage(), 'Duplicate entry') !== false) {
                echo "<p style='color: orange;'>⚠️ Student {$siswa[0]} already exists</p>";
            } else {
                throw $e;
            }
        }
    }
    
    // 11. Verification
    echo "<h3>🔍 Verification Results:</h3>";
    
    $waliKelasUsers = $db->fetchAll("SELECT username, nama_lengkap FROM users WHERE role = 'wali_kelas'");
    echo "<p><strong>Wali Kelas Users:</strong></p>";
    echo "<ul>";
    foreach ($waliKelasUsers as $user) {
        echo "<li>{$user['nama_lengkap']} ({$user['username']})</li>";
    }
    echo "</ul>";
    
    $kelasWithWali = $db->fetchAll("
        SELECT k.nama_kelas, k.wali_kelas, u.nama_lengkap as wali_nama, u.username as wali_username
        FROM kelas k 
        LEFT JOIN users u ON k.wali_kelas_id = u.id 
        WHERE k.wali_kelas_id IS NOT NULL
    ");
    echo "<p><strong>Kelas with Wali Kelas:</strong></p>";
    echo "<ul>";
    foreach ($kelasWithWali as $kelas) {
        echo "<li>{$kelas['nama_kelas']} - {$kelas['wali_nama']} ({$kelas['wali_username']})</li>";
    }
    echo "</ul>";
    
    $mappings = $db->fetchAll("
        SELECT u.nama_lengkap, k.nama_kelas 
        FROM user_kelas_mapping ukm 
        JOIN users u ON ukm.user_id = u.id 
        JOIN kelas k ON ukm.kelas_id = k.id
        WHERE ukm.role_type = 'wali_kelas'
    ");
    echo "<p><strong>User-Kelas Mappings:</strong></p>";
    echo "<ul>";
    foreach ($mappings as $mapping) {
        echo "<li>{$mapping['nama_lengkap']} → {$mapping['nama_kelas']}</li>";
    }
    echo "</ul>";
    
    echo "<h2 style='color: green;'>🎉 Database update completed successfully!</h2>";
    echo "<p><strong>Login credentials for testing:</strong></p>";
    echo "<ul>";
    echo "<li><strong>Admin:</strong> admin / admin123</li>";
    echo "<li><strong>Wali Kelas X-IPA-1:</strong> wali_x_ipa1 / password</li>";
    echo "<li><strong>Wali Kelas X-IPS-1:</strong> wali_x_ips1 / password</li>";
    echo "<li><strong>Wali Kelas XI-IPA-1:</strong> wali_xi_ipa1 / password</li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<h2 style='color: red;'>❌ Error updating database:</h2>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
