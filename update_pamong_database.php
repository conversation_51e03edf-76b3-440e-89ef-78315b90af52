<?php
// Script untuk update database Pamong Roles dan Staff
// File: update_pamong_database.php

require_once __DIR__ . '/app/models/Database.php';

try {
    echo "<h2>🔄 Updating Database for Pamong Roles and Staff...</h2>";
    
    $db = new Database();
    
    // 1. Update enum role untuk menambah pamong roles
    echo "<p>1. Updating user roles enum for Pamong...</p>";
    $db->query("ALTER TABLE users MODIFY COLUMN role ENUM(
        'admin', 
        'pamong_mp',
        'pamong_mt', 
        'pamong_mm',
        'pamong_mu',
        'wali_kelas', 
        'staff'
    ) DEFAULT 'staff'");
    echo "<p style='color: green;'>✅ User roles updated with Pamong types</p>";
    
    // 2. Tambah kolom pamong_type
    echo "<p>2. Adding pamong_type column...</p>";
    try {
        $db->query("ALTER TABLE users ADD COLUMN pamong_type ENUM('mp', 'mt', 'mm', 'mu') NULL AFTER role");
        echo "<p style='color: green;'>✅ Column pamong_type added</p>";
    } catch (Exception $e) {
        if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
            echo "<p style='color: orange;'>⚠️ Column pamong_type already exists</p>";
        } else {
            throw $e;
        }
    }
    
    // 3. Tambah kolom tingkat_akses
    echo "<p>3. Adding tingkat_akses column...</p>";
    try {
        $db->query("ALTER TABLE users ADD COLUMN tingkat_akses JSON NULL AFTER pamong_type");
        echo "<p style='color: green;'>✅ Column tingkat_akses added</p>";
    } catch (Exception $e) {
        if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
            echo "<p style='color: orange;'>⚠️ Column tingkat_akses already exists</p>";
        } else {
            throw $e;
        }
    }
    
    // 4. Insert sample pamong users
    echo "<p>4. Creating sample Pamong users...</p>";
    $pamongUsers = [
        ['pamong_mp', '<EMAIL>', 'Pamong MP - Budi Hartono', 'pamong_mp', 'mp', '["KPP"]'],
        ['pamong_mt', '<EMAIL>', 'Pamong MT - Sari Dewi', 'pamong_mt', 'mt', '["X"]'],
        ['pamong_mm', '<EMAIL>', 'Pamong MM - Ahmad Yusuf', 'pamong_mm', 'mm', '["XI"]'],
        ['pamong_mu', '<EMAIL>', 'Pamong MU - Rina Sari', 'pamong_mu', 'mu', '["XII", "KPA"]']
    ];
    
    foreach ($pamongUsers as $user) {
        try {
            $db->query(
                "INSERT INTO users (username, email, password, role, pamong_type, nama_lengkap, tingkat_akses, is_active) VALUES (?, ?, ?, ?, ?, ?, ?, TRUE)",
                [$user[0], $user[1], '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', $user[3], $user[4], $user[2], $user[5]]
            );
            echo "<p style='color: green;'>✅ Created Pamong: {$user[2]} ({$user[0]})</p>";
        } catch (Exception $e) {
            if (strpos($e->getMessage(), 'Duplicate entry') !== false) {
                echo "<p style='color: orange;'>⚠️ Pamong {$user[0]} already exists</p>";
            } else {
                throw $e;
            }
        }
    }
    
    // 5. Insert sample staff user
    echo "<p>5. Creating sample Staff user...</p>";
    try {
        $db->query(
            "INSERT INTO users (username, email, password, role, nama_lengkap, is_active) VALUES (?, ?, ?, 'staff', ?, TRUE)",
            ['staff_tata_usaha', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Staff Tata Usaha - Indah Permata']
        );
        echo "<p style='color: green;'>✅ Created Staff: Staff Tata Usaha - Indah Permata (staff_tata_usaha)</p>";
    } catch (Exception $e) {
        if (strpos($e->getMessage(), 'Duplicate entry') !== false) {
            echo "<p style='color: orange;'>⚠️ Staff staff_tata_usaha already exists</p>";
        } else {
            throw $e;
        }
    }
    
    // 6. Update kategori catatan allowed_roles untuk pamong
    echo "<p>6. Updating kategori catatan allowed roles...</p>";
    $db->query("UPDATE kategori_catatan SET allowed_roles = '[\"admin\", \"pamong_mp\", \"pamong_mt\", \"pamong_mm\", \"pamong_mu\"]' WHERE kode_kategori LIKE 'pamong_%'");
    echo "<p style='color: green;'>✅ Allowed roles updated for pamong categories</p>";
    
    // 7. Tambah beberapa kelas sample untuk testing
    echo "<p>7. Adding sample classes for testing...</p>";
    $sampleKelas = [
        ['KPP-A', 'KPP', 'Umum'],
        ['KPP-B', 'KPP', 'Umum'],
        ['X-IPA-2', 'X', 'IPA'],
        ['X-IPS-2', 'X', 'IPS'],
        ['XI-IPA-2', 'XI', 'IPA'],
        ['XI-IPS-1', 'XI', 'IPS'],
        ['XII-IPA-1', 'XII', 'IPA'],
        ['XII-IPS-1', 'XII', 'IPS'],
        ['KPA-A', 'KPA', 'Umum'],
        ['KPA-B', 'KPA', 'Umum']
    ];
    
    foreach ($sampleKelas as $kelas) {
        try {
            $db->query(
                "INSERT INTO kelas (nama_kelas, tingkat, jurusan, tahun_pelajaran, created_by) VALUES (?, ?, ?, '2024/2025', 1)",
                $kelas
            );
            echo "<p style='color: green;'>✅ Added class: {$kelas[0]} ({$kelas[1]} - {$kelas[2]})</p>";
        } catch (Exception $e) {
            if (strpos($e->getMessage(), 'Duplicate entry') !== false) {
                echo "<p style='color: orange;'>⚠️ Class {$kelas[0]} already exists</p>";
            } else {
                throw $e;
            }
        }
    }
    
    // 8. Tambah sample siswa untuk testing pamong access
    echo "<p>8. Adding sample students for testing Pamong access...</p>";
    
    // Get kelas IDs
    $kelasMap = [];
    $allKelas = $db->fetchAll("SELECT id, nama_kelas FROM kelas");
    foreach ($allKelas as $kelas) {
        $kelasMap[$kelas['nama_kelas']] = $kelas['id'];
    }
    
    $sampleSiswa = [
        // KPP students
        ['2024101', 'Andi Pratama', 'L', 'KPP-A'],
        ['2024102', 'Siti Aisyah', 'P', 'KPP-A'],
        ['2024103', 'Budi Santoso', 'L', 'KPP-B'],
        
        // X students  
        ['2024201', 'Dewi Lestari', 'P', 'X-IPA-2'],
        ['2024202', 'Rizki Maulana', 'L', 'X-IPS-2'],
        
        // XI students
        ['2024301', 'Fitri Handayani', 'P', 'XI-IPA-2'],
        ['2024302', 'Agus Setiawan', 'L', 'XI-IPS-1'],
        
        // XII students
        ['2024401', 'Maya Sari', 'P', 'XII-IPA-1'],
        ['2024402', 'Doni Prasetyo', 'L', 'XII-IPS-1'],
        
        // KPA students
        ['2024501', 'Lina Marlina', 'P', 'KPA-A'],
        ['2024502', 'Eko Wijaya', 'L', 'KPA-B']
    ];
    
    foreach ($sampleSiswa as $siswa) {
        if (isset($kelasMap[$siswa[3]])) {
            try {
                $db->query(
                    "INSERT INTO siswa (nis, nama_lengkap, jenis_kelamin, kelas_id, tahun_masuk, status_siswa, created_by) VALUES (?, ?, ?, ?, 2024, 'aktif', 1)",
                    [$siswa[0], $siswa[1], $siswa[2], $kelasMap[$siswa[3]]]
                );
                echo "<p style='color: green;'>✅ Added student: {$siswa[1]} ({$siswa[0]}) - {$siswa[3]}</p>";
            } catch (Exception $e) {
                if (strpos($e->getMessage(), 'Duplicate entry') !== false) {
                    echo "<p style='color: orange;'>⚠️ Student {$siswa[0]} already exists</p>";
                } else {
                    throw $e;
                }
            }
        }
    }
    
    // 9. Tambah tabel user_management_log
    echo "<p>9. Creating user management log table...</p>";
    try {
        $db->query("
            CREATE TABLE IF NOT EXISTS user_management_log (
                id INT PRIMARY KEY AUTO_INCREMENT,
                admin_user_id INT NOT NULL,
                target_user_id INT NOT NULL,
                action_type ENUM('create', 'update', 'delete', 'password_reset') NOT NULL,
                old_values JSON NULL,
                new_values JSON NULL,
                ip_address VARCHAR(45),
                user_agent TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                
                FOREIGN KEY (admin_user_id) REFERENCES users(id),
                FOREIGN KEY (target_user_id) REFERENCES users(id),
                INDEX idx_admin_user (admin_user_id),
                INDEX idx_target_user (target_user_id),
                INDEX idx_action_type (action_type),
                INDEX idx_created_at (created_at)
            )
        ");
        echo "<p style='color: green;'>✅ User management log table created</p>";
    } catch (Exception $e) {
        echo "<p style='color: orange;'>⚠️ User management log table might already exist</p>";
    }
    
    // 10. Tambah indexes untuk performance
    echo "<p>10. Adding performance indexes...</p>";
    try {
        $db->query("CREATE INDEX idx_users_role_pamong ON users(role, pamong_type)");
        echo "<p style='color: green;'>✅ Index idx_users_role_pamong added</p>";
    } catch (Exception $e) {
        echo "<p style='color: orange;'>⚠️ Index might already exist</p>";
    }
    
    try {
        $db->query("CREATE INDEX idx_kelas_tingkat_active ON kelas(tingkat, is_active)");
        echo "<p style='color: green;'>✅ Index idx_kelas_tingkat_active added</p>";
    } catch (Exception $e) {
        echo "<p style='color: orange;'>⚠️ Index might already exist</p>";
    }
    
    try {
        $db->query("CREATE INDEX idx_siswa_status_kelas ON siswa(status_siswa, kelas_id)");
        echo "<p style='color: green;'>✅ Index idx_siswa_status_kelas added</p>";
    } catch (Exception $e) {
        echo "<p style='color: orange;'>⚠️ Index might already exist</p>";
    }
    
    // 11. Verification
    echo "<h3>🔍 Verification Results:</h3>";
    
    $pamongUsers = $db->fetchAll("SELECT username, nama_lengkap, role, pamong_type FROM users WHERE role LIKE 'pamong_%'");
    echo "<p><strong>Pamong Users:</strong></p>";
    echo "<ul>";
    foreach ($pamongUsers as $user) {
        echo "<li>{$user['nama_lengkap']} ({$user['username']}) - {$user['role']}</li>";
    }
    echo "</ul>";
    
    $staffUsers = $db->fetchAll("SELECT username, nama_lengkap, role FROM users WHERE role = 'staff'");
    echo "<p><strong>Staff Users:</strong></p>";
    echo "<ul>";
    foreach ($staffUsers as $user) {
        echo "<li>{$user['nama_lengkap']} ({$user['username']})</li>";
    }
    echo "</ul>";
    
    $siswaByTingkat = $db->fetchAll("
        SELECT k.tingkat, COUNT(*) as jumlah_siswa 
        FROM siswa s 
        JOIN kelas k ON s.kelas_id = k.id 
        WHERE s.status_siswa = 'aktif'
        GROUP BY k.tingkat 
        ORDER BY 
            CASE k.tingkat 
                WHEN 'KPP' THEN 1 
                WHEN 'X' THEN 2 
                WHEN 'XI' THEN 3 
                WHEN 'XII' THEN 4 
                WHEN 'KPA' THEN 5 
                ELSE 6 
            END
    ");
    echo "<p><strong>Students by Tingkat:</strong></p>";
    echo "<ul>";
    foreach ($siswaByTingkat as $tingkat) {
        echo "<li>Tingkat {$tingkat['tingkat']}: {$tingkat['jumlah_siswa']} siswa</li>";
    }
    echo "</ul>";
    
    echo "<h2 style='color: green;'>🎉 Database update completed successfully!</h2>";
    echo "<p><strong>Login credentials for testing:</strong></p>";
    echo "<ul>";
    echo "<li><strong>Admin:</strong> admin / admin123</li>";
    echo "<li><strong>Pamong MP (KPP):</strong> pamong_mp / password</li>";
    echo "<li><strong>Pamong MT (X):</strong> pamong_mt / password</li>";
    echo "<li><strong>Pamong MM (XI):</strong> pamong_mm / password</li>";
    echo "<li><strong>Pamong MU (XII, KPA):</strong> pamong_mu / password</li>";
    echo "<li><strong>Staff (Read-only):</strong> staff_tata_usaha / password</li>";
    echo "<li><strong>Wali Kelas X-IPA-1:</strong> wali_x_ipa1 / password</li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<h2 style='color: red;'>❌ Error updating database:</h2>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
