<?php
/**
 * Script untuk menambahkan data sample kelas
 */

require_once 'app/config/db.php';

try {
    $pdo = getDBConnection();
    
    // Sample data kelas
    $kelasData = [
        [
            'nama_kelas' => 'X-IPA-1',
            'tingkat' => 10,
            'jurusan' => 'IPA',
            'tahun_pelajaran' => '2024/2025',
            'wali_kelas' => 'Budi Santoso, S.Pd',
            'kapasitas' => 32
        ],
        [
            'nama_kelas' => 'X-IPA-2',
            'tingkat' => 10,
            'jurusan' => 'IPA',
            'tahun_pelajaran' => '2024/2025',
            'wali_kelas' => 'Siti Aminah, S.Pd',
            'kapasitas' => 30
        ],
        [
            'nama_kelas' => 'X-IPS-1',
            'tingkat' => 10,
            'jurusan' => 'IPS',
            'tahun_pelajaran' => '2024/2025',
            'wali_kelas' => '<PERSON>, S.Pd',
            'kapasitas' => 28
        ],
        [
            'nama_kelas' => 'XI-IPA-1',
            'tingkat' => 11,
            'jurusan' => 'IPA',
            'tahun_pelajaran' => '2024/2025',
            'wali_kelas' => 'Dewi Sartika, S.Pd',
            'kapasitas' => 30
        ],
        [
            'nama_kelas' => 'XI-IPS-1',
            'tingkat' => 11,
            'jurusan' => 'IPS',
            'tahun_pelajaran' => '2024/2025',
            'wali_kelas' => 'Rudi Hartono, S.Pd',
            'kapasitas' => 25
        ],
        [
            'nama_kelas' => 'XII-IPA-1',
            'tingkat' => 12,
            'jurusan' => 'IPA',
            'tahun_pelajaran' => '2024/2025',
            'wali_kelas' => 'Rina Marlina, S.Pd',
            'kapasitas' => 28
        ]
    ];
    
    // Check if kelas table exists and has data
    $checkTable = $pdo->query("SHOW TABLES LIKE 'kelas'");
    if ($checkTable->rowCount() == 0) {
        echo "Tabel kelas tidak ditemukan. Pastikan database sudah diimport.\n";
        exit;
    }
    
    // Check if data already exists
    $existingData = $pdo->query("SELECT COUNT(*) as count FROM kelas")->fetch();
    if ($existingData['count'] > 0) {
        echo "Data kelas sudah ada (" . $existingData['count'] . " records). Menambahkan data baru...\n";
    }
    
    $insertedCount = 0;
    
    foreach ($kelasData as $kelas) {
        // Check if class already exists
        $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM kelas WHERE nama_kelas = ? AND tahun_pelajaran = ?");
        $stmt->execute([$kelas['nama_kelas'], $kelas['tahun_pelajaran']]);
        $exists = $stmt->fetch();
        
        if ($exists['count'] == 0) {
            // Insert new class
            $insertStmt = $pdo->prepare("
                INSERT INTO kelas (nama_kelas, tingkat, jurusan, tahun_pelajaran, wali_kelas, kapasitas, created_by, created_at) 
                VALUES (?, ?, ?, ?, ?, ?, 1, NOW())
            ");
            
            $result = $insertStmt->execute([
                $kelas['nama_kelas'],
                $kelas['tingkat'],
                $kelas['jurusan'],
                $kelas['tahun_pelajaran'],
                $kelas['wali_kelas'],
                $kelas['kapasitas']
            ]);
            
            if ($result) {
                echo "✓ Berhasil menambahkan kelas: " . $kelas['nama_kelas'] . "\n";
                $insertedCount++;
            } else {
                echo "✗ Gagal menambahkan kelas: " . $kelas['nama_kelas'] . "\n";
            }
        } else {
            echo "- Kelas " . $kelas['nama_kelas'] . " sudah ada, dilewati.\n";
        }
    }
    
    echo "\n=== SUMMARY ===\n";
    echo "Total kelas baru yang ditambahkan: $insertedCount\n";
    
    // Show current data
    $allKelas = $pdo->query("SELECT nama_kelas, tingkat, jurusan, wali_kelas FROM kelas WHERE is_active = 1 ORDER BY tingkat, nama_kelas")->fetchAll();
    echo "Total kelas aktif saat ini: " . count($allKelas) . "\n\n";
    
    echo "Daftar kelas:\n";
    foreach ($allKelas as $k) {
        echo "- " . $k['nama_kelas'] . " (Tingkat " . $k['tingkat'] . ", " . $k['jurusan'] . ") - Wali: " . $k['wali_kelas'] . "\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Pastikan database sudah dikonfigurasi dengan benar.\n";
}
?>
