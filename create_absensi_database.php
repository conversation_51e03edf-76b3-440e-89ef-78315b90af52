<?php
// <PERSON>ript untuk membuat database absensi
// File: create_absensi_database.php

require_once __DIR__ . '/app/models/Database.php';

try {
    echo "<h2>📅 Creating Absensi Database Structure...</h2>";
    
    $db = new Database();
    
    // 1. Create absensi table
    echo "<p>1. Creating absensi table...</p>";
    $db->query("
        CREATE TABLE IF NOT EXISTS absensi (
            id INT PRIMARY KEY AUTO_INCREMENT,
            siswa_id INT NOT NULL,
            tanggal DATE NOT NULL,
            jenis_ketidakhadiran ENUM('sakit', 'ijin', 'alpha') NOT NULL,
            keterangan TEXT NULL,
            surat_keterangan VARCHAR(255) NULL,
            jam_masuk TIME NULL,
            jam_keluar TIME NULL,
            created_by INT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            
            FOREIGN KEY (siswa_id) REFERENCES siswa(id_siswa) ON DELETE CASCADE,
            FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE RESTRICT,
            
            INDEX idx_siswa_tanggal (siswa_id, tanggal),
            INDEX idx_tanggal (tanggal),
            INDEX idx_jenis (jenis_ketidakhadiran),
            INDEX idx_created_by (created_by),
            
            UNIQUE KEY unique_siswa_tanggal (siswa_id, tanggal)
        )
    ");
    echo "<p style='color: green;'>✅ Absensi table created</p>";
    
    // 2. Create activity_log table if not exists
    echo "<p>2. Creating activity_log table...</p>";
    try {
        $db->query("
            CREATE TABLE IF NOT EXISTS activity_log (
                id INT PRIMARY KEY AUTO_INCREMENT,
                table_name VARCHAR(50) NOT NULL,
                record_id INT NOT NULL,
                action_type ENUM('INSERT', 'UPDATE', 'DELETE') NOT NULL,
                user_id INT NULL,
                description TEXT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                
                INDEX idx_table_record (table_name, record_id),
                INDEX idx_user_id (user_id),
                INDEX idx_created_at (created_at)
            )
        ");
        echo "<p style='color: green;'>✅ Activity log table created</p>";
    } catch (Exception $e) {
        echo "<p style='color: orange;'>⚠️ Activity log table might already exist</p>";
    }
    
    // 3. Create views
    echo "<p>3. Creating absensi views...</p>";
    $db->query("
        CREATE OR REPLACE VIEW view_absensi_summary AS
        SELECT 
            s.id_siswa,
            s.nis,
            s.nama_lengkap,
            k.nama_kelas,
            k.tingkat,
            COUNT(a.id) as total_ketidakhadiran,
            SUM(CASE WHEN a.jenis_ketidakhadiran = 'sakit' THEN 1 ELSE 0 END) as total_sakit,
            SUM(CASE WHEN a.jenis_ketidakhadiran = 'ijin' THEN 1 ELSE 0 END) as total_ijin,
            SUM(CASE WHEN a.jenis_ketidakhadiran = 'alpha' THEN 1 ELSE 0 END) as total_alpha,
            MAX(a.tanggal) as ketidakhadiran_terakhir
        FROM siswa s
        LEFT JOIN kelas k ON s.kelas_id = k.id
        LEFT JOIN absensi a ON s.id_siswa = a.siswa_id
        WHERE s.status_siswa = 'aktif'
        GROUP BY s.id_siswa, s.nis, s.nama_lengkap, k.nama_kelas, k.tingkat
    ");
    echo "<p style='color: green;'>✅ Absensi summary view created</p>";
    
    // 4. Insert sample data
    echo "<p>4. Adding sample absensi data...</p>";
    
    // Get some student IDs for sample data
    $students = $db->fetchAll("SELECT id_siswa FROM siswa WHERE status_siswa = 'aktif' LIMIT 10");
    
    if (!empty($students)) {
        $sampleAbsensi = [
            [$students[0]['id_siswa'], '2024-12-01', 'sakit', 'Demam tinggi, istirahat di rumah'],
            [$students[0]['id_siswa'], '2024-12-02', 'sakit', 'Masih demam, kontrol ke dokter'],
            [$students[1]['id_siswa'], '2024-12-03', 'ijin', 'Acara keluarga di luar kota'],
            [$students[2]['id_siswa'], '2024-12-04', 'alpha', 'Tidak ada keterangan'],
            [$students[3]['id_siswa'], '2024-12-05', 'sakit', 'Flu dan batuk'],
            [$students[4]['id_siswa'], '2024-12-06', 'ijin', 'Mengurus dokumen penting'],
            [$students[0]['id_siswa'], '2024-12-09', 'alpha', 'Terlambat bangun, tidak masuk sekolah'],
            [$students[1]['id_siswa'], '2024-12-10', 'sakit', 'Sakit perut, ke puskesmas'],
            [$students[5]['id_siswa'], '2024-12-11', 'ijin', 'Menjenguk nenek yang sakit'],
            [$students[6]['id_siswa'], '2024-12-12', 'sakit', 'Pusing dan mual']
        ];
        
        foreach ($sampleAbsensi as $absen) {
            try {
                $db->query(
                    "INSERT IGNORE INTO absensi (siswa_id, tanggal, jenis_ketidakhadiran, keterangan, created_by) VALUES (?, ?, ?, ?, 1)",
                    $absen
                );
                echo "<p style='color: green;'>✅ Added absensi: {$absen[2]} on {$absen[1]}</p>";
            } catch (Exception $e) {
                echo "<p style='color: orange;'>⚠️ Absensi might already exist for {$absen[1]}</p>";
            }
        }
    }
    
    // 5. Create additional indexes
    echo "<p>5. Creating performance indexes...</p>";
    try {
        $db->query("CREATE INDEX idx_absensi_siswa_month ON absensi(siswa_id, YEAR(tanggal), MONTH(tanggal))");
        echo "<p style='color: green;'>✅ Monthly index created</p>";
    } catch (Exception $e) {
        echo "<p style='color: orange;'>⚠️ Index might already exist</p>";
    }
    
    try {
        $db->query("CREATE INDEX idx_absensi_jenis_tanggal ON absensi(jenis_ketidakhadiran, tanggal)");
        echo "<p style='color: green;'>✅ Type-date index created</p>";
    } catch (Exception $e) {
        echo "<p style='color: orange;'>⚠️ Index might already exist</p>";
    }
    
    // 6. Verification
    echo "<h3>🔍 Verification Results:</h3>";
    
    $totalAbsensi = $db->fetch("SELECT COUNT(*) as total FROM absensi");
    echo "<p><strong>Total Absensi Records:</strong> {$totalAbsensi['total']}</p>";
    
    $absensiByType = $db->fetchAll("SELECT jenis_ketidakhadiran, COUNT(*) as count FROM absensi GROUP BY jenis_ketidakhadiran");
    echo "<p><strong>Absensi by Type:</strong></p>";
    echo "<ul>";
    foreach ($absensiByType as $type) {
        $icon = '';
        switch ($type['jenis_ketidakhadiran']) {
            case 'sakit':
                $icon = '🤒';
                break;
            case 'ijin':
                $icon = '📝';
                break;
            case 'alpha':
                $icon = '❌';
                break;
        }
        echo "<li>{$icon} " . ucfirst($type['jenis_ketidakhadiran']) . ": {$type['count']} records</li>";
    }
    echo "</ul>";
    
    // Sample summary
    $summary = $db->fetchAll("
        SELECT 
            s.nama_lengkap,
            k.nama_kelas,
            COUNT(a.id) as total_absen,
            SUM(CASE WHEN a.jenis_ketidakhadiran = 'sakit' THEN 1 ELSE 0 END) as sakit,
            SUM(CASE WHEN a.jenis_ketidakhadiran = 'ijin' THEN 1 ELSE 0 END) as ijin,
            SUM(CASE WHEN a.jenis_ketidakhadiran = 'alpha' THEN 1 ELSE 0 END) as alpha
        FROM siswa s
        LEFT JOIN kelas k ON s.kelas_id = k.id
        LEFT JOIN absensi a ON s.id_siswa = a.siswa_id
        WHERE a.id IS NOT NULL
        GROUP BY s.id_siswa, s.nama_lengkap, k.nama_kelas
        ORDER BY total_absen DESC
        LIMIT 5
    ");
    
    if (!empty($summary)) {
        echo "<p><strong>Top 5 Students with Absences:</strong></p>";
        echo "<table border='1' cellpadding='8' cellspacing='0' style='border-collapse: collapse; width: 100%; margin-top: 10px;'>";
        echo "<thead style='background-color: #f8f9fa;'>";
        echo "<tr><th>Nama Siswa</th><th>Kelas</th><th>Total</th><th>🤒 Sakit</th><th>📝 Ijin</th><th>❌ Alpha</th></tr>";
        echo "</thead><tbody>";
        
        foreach ($summary as $row) {
            echo "<tr>";
            echo "<td>{$row['nama_lengkap']}</td>";
            echo "<td>{$row['nama_kelas']}</td>";
            echo "<td style='text-align: center; font-weight: bold;'>{$row['total_absen']}</td>";
            echo "<td style='text-align: center; color: #dc3545;'>{$row['sakit']}</td>";
            echo "<td style='text-align: center; color: #ffc107;'>{$row['ijin']}</td>";
            echo "<td style='text-align: center; color: #6c757d;'>{$row['alpha']}</td>";
            echo "</tr>";
        }
        
        echo "</tbody></table>";
    }
    
    echo "<div style='margin-top: 20px; padding: 20px; background-color: #d4edda; border: 1px solid #c3e6cb; border-radius: 5px;'>";
    echo "<h4 style='color: #155724; margin-bottom: 10px;'>📊 Absensi System Features:</h4>";
    echo "<ul style='color: #155724;'>";
    echo "<li><strong>Jenis Ketidakhadiran:</strong> Sakit, Ijin, Alpha</li>";
    echo "<li><strong>Keterangan:</strong> Detailed description for each absence</li>";
    echo "<li><strong>File Upload:</strong> Support for medical certificates</li>";
    echo "<li><strong>Access Control:</strong> Wali kelas and admin can manage</li>";
    echo "<li><strong>Reporting:</strong> Summary views and statistics</li>";
    echo "<li><strong>Audit Trail:</strong> Complete logging system</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h2 style='color: green; margin-top: 30px;'>🎉 Absensi database created successfully!</h2>";
    echo "<p><strong>Next steps:</strong></p>";
    echo "<ol>";
    echo "<li>Create Absensi model and controller</li>";
    echo "<li>Add absensi section to student detail page</li>";
    echo "<li>Create absensi management interface</li>";
    echo "<li>Add reporting features</li>";
    echo "</ol>";
    
} catch (Exception $e) {
    echo "<h2 style='color: red;'>❌ Error creating absensi database:</h2>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 20px;
    background-color: #f8f9fa;
}

h2, h3 {
    color: #495057;
}

table {
    background-color: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border-radius: 8px;
    overflow: hidden;
}

th {
    background-color: #007bff !important;
    color: white !important;
    font-weight: 600;
}

td {
    padding: 8px !important;
}

tr:nth-child(even) {
    background-color: #f8f9fa;
}

tr:hover {
    background-color: #e9ecef;
}

p {
    margin: 5px 0;
    padding: 5px 10px;
    border-radius: 4px;
}

p[style*="green"] {
    background-color: #d4edda;
    border-left: 4px solid #28a745;
}

p[style*="orange"] {
    background-color: #fff3cd;
    border-left: 4px solid #ffc107;
}

ul, ol {
    margin: 10px 0;
}

li {
    margin: 5px 0;
}
</style>
