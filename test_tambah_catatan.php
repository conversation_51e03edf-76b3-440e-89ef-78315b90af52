<?php
/**
 * Test Tambah Catatan Functionality
 * Comprehensive test for the enhanced catatan system
 */

require_once 'app/models/Database.php';
require_once 'app/models/CatatanSiswa.php';
require_once 'app/models/Siswa.php';
require_once 'app/helpers/Security.php';

echo "<h2>🧪 Test Tambah Catatan Functionality</h2>";

// Start session for testing
session_start();
$_SESSION['user_id'] = 1;
$_SESSION['user_role'] = 'admin';
$_SESSION['username'] = 'admin';

try {
    $db = new Database();
    $catatanModel = new CatatanSiswa();
    $siswaModel = new Siswa();
    
    echo "<h3>📋 Pre-Test Checks</h3>";
    
    // Check if tables exist
    $tables = ['catatan_siswa', 'kategori_catatan', 'siswa', 'users'];
    foreach ($tables as $table) {
        $result = $db->fetch("SHOW TABLES LIKE ?", [$table]);
        if ($result) {
            echo "<p style='color: green;'>✅ Table {$table} exists</p>";
        } else {
            echo "<p style='color: red;'>❌ Table {$table} missing</p>";
            exit;
        }
    }
    
    // Check if test siswa exists
    $testSiswa = $siswaModel->getById(1);
    if (!$testSiswa) {
        echo "<p style='color: orange;'>⚠️ Creating test siswa...</p>";
        
        // Create test siswa
        $siswaData = [
            'nis' => 'TEST001',
            'nama_lengkap' => 'Test Siswa Catatan',
            'jenis_kelamin' => 'L',
            'kelas_id' => 1,
            'tahun_masuk' => 2024,
            'created_by' => 1
        ];
        
        $siswaId = $siswaModel->create($siswaData);
        if ($siswaId) {
            echo "<p style='color: green;'>✅ Test siswa created with ID: {$siswaId}</p>";
            $testSiswa = $siswaModel->getById($siswaId);
        }
    } else {
        echo "<p style='color: green;'>✅ Test siswa found: {$testSiswa['nama_lengkap']}</p>";
    }
    
    // Check categories
    $categories = $catatanModel->getCategoriesGrouped();
    echo "<p style='color: green;'>✅ Found " . count($categories) . " category groups</p>";
    
    echo "<h3>🧪 Testing Catatan Creation</h3>";
    
    // Test 1: Valid catatan creation
    echo "<h4>Test 1: Valid Catatan Creation</h4>";
    $testData1 = [
        'siswa_id' => $testSiswa['id_siswa'],
        'jenis_catatan' => 'pamong_mp',
        'judul_catatan' => 'Test Catatan Automated',
        'isi_catatan' => 'Ini adalah test catatan yang dibuat secara otomatis untuk menguji fungsi tambah catatan.',
        'tanggal_catatan' => date('Y-m-d'),
        'tingkat_prioritas' => 'sedang',
        'status_catatan' => 'aktif',
        'created_by' => 1
    ];
    
    try {
        $result1 = $catatanModel->create($testData1);
        if ($result1) {
            echo "<p style='color: green;'>✅ Test 1 PASSED: Catatan created with ID {$result1}</p>";
        } else {
            echo "<p style='color: red;'>❌ Test 1 FAILED: Could not create catatan</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Test 1 FAILED: " . $e->getMessage() . "</p>";
    }
    
    // Test 2: Invalid data (missing required field)
    echo "<h4>Test 2: Invalid Data (Missing Required Field)</h4>";
    $testData2 = [
        'siswa_id' => $testSiswa['id_siswa'],
        'jenis_catatan' => 'pamong_mp',
        // Missing judul_catatan
        'isi_catatan' => 'Test catatan tanpa judul',
        'created_by' => 1
    ];
    
    try {
        $result2 = $catatanModel->create($testData2);
        echo "<p style='color: red;'>❌ Test 2 FAILED: Should have thrown exception</p>";
    } catch (Exception $e) {
        echo "<p style='color: green;'>✅ Test 2 PASSED: Correctly caught validation error: " . $e->getMessage() . "</p>";
    }
    
    // Test 3: Invalid siswa ID
    echo "<h4>Test 3: Invalid Siswa ID</h4>";
    $testData3 = [
        'siswa_id' => 99999, // Non-existent siswa
        'jenis_catatan' => 'pamong_mp',
        'judul_catatan' => 'Test Invalid Siswa',
        'isi_catatan' => 'Test dengan siswa ID yang tidak valid',
        'created_by' => 1
    ];
    
    try {
        $result3 = $catatanModel->create($testData3);
        echo "<p style='color: red;'>❌ Test 3 FAILED: Should have thrown exception</p>";
    } catch (Exception $e) {
        echo "<p style='color: green;'>✅ Test 3 PASSED: Correctly caught siswa validation error: " . $e->getMessage() . "</p>";
    }
    
    // Test 4: Invalid jenis catatan
    echo "<h4>Test 4: Invalid Jenis Catatan</h4>";
    $testData4 = [
        'siswa_id' => $testSiswa['id_siswa'],
        'jenis_catatan' => 'invalid_category',
        'judul_catatan' => 'Test Invalid Category',
        'isi_catatan' => 'Test dengan kategori yang tidak valid',
        'created_by' => 1
    ];
    
    try {
        $result4 = $catatanModel->create($testData4);
        echo "<p style='color: red;'>❌ Test 4 FAILED: Should have thrown exception</p>";
    } catch (Exception $e) {
        echo "<p style='color: green;'>✅ Test 4 PASSED: Correctly caught category validation error: " . $e->getMessage() . "</p>";
    }
    
    // Test 5: Security access check
    echo "<h4>Test 5: Security Access Check</h4>";
    $canAccess = Security::canAccessSiswa($testSiswa['id_siswa']);
    if ($canAccess) {
        echo "<p style='color: green;'>✅ Test 5 PASSED: User can access siswa</p>";
    } else {
        echo "<p style='color: red;'>❌ Test 5 FAILED: User cannot access siswa</p>";
    }
    
    // Test 6: Category filtering by role
    echo "<h4>Test 6: Category Filtering by Role</h4>";
    $filteredCategories = Security::filterCatatanCategoriesByRole($categories);
    if (!empty($filteredCategories)) {
        echo "<p style='color: green;'>✅ Test 6 PASSED: Categories filtered for admin role</p>";
        foreach ($filteredCategories as $groupName => $groupCategories) {
            echo "<p style='margin-left: 20px;'>- {$groupName}: " . count($groupCategories) . " categories</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ Test 6 FAILED: No categories available for admin</p>";
    }
    
    echo "<h3>📊 Test Results Summary</h3>";
    
    // Get all catatan for test siswa
    $allCatatan = $catatanModel->getBySiswaId($testSiswa['id_siswa']);
    echo "<p><strong>Total catatan for test siswa:</strong> " . count($allCatatan) . "</p>";
    
    if (!empty($allCatatan)) {
        echo "<h4>Recent Catatan:</h4>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin-top: 10px;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th>ID</th><th>Kategori</th><th>Judul</th><th>Tanggal</th><th>Status</th>";
        echo "</tr>";
        
        foreach (array_slice($allCatatan, 0, 5) as $catatan) {
            echo "<tr>";
            echo "<td>{$catatan['id']}</td>";
            echo "<td>{$catatan['nama_kategori']}</td>";
            echo "<td>{$catatan['judul_catatan']}</td>";
            echo "<td>{$catatan['tanggal_catatan']}</td>";
            echo "<td>{$catatan['status_catatan']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<h3>✅ All Tests Completed!</h3>";
    echo "<p style='color: green; font-weight: bold;'>🎉 Tambah Catatan functionality is working correctly!</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Test Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}

echo "<hr>";
echo "<p><a href='public/catatan/add/1'>🧪 Test Add Catatan Form</a></p>";
echo "<p><a href='public/siswa/detail/1'>👤 View Test Siswa Detail</a></p>";
echo "<p><a href='public/siswa'>📋 Back to Student List</a></p>";
?>
