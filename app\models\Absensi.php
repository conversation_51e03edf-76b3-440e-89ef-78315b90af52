<?php
require_once __DIR__ . '/Database.php';

class Absensi {
    private $db;
    
    public function __construct() {
        $this->db = new Database();
    }
    
    /**
     * Get all absensi records for a student
     */
    public function getAbsensiByStudent($siswaId, $startDate = null, $endDate = null) {
        try {
            $startDate = $startDate ?: date('Y-m-01'); // Default to start of current month
            $endDate = $endDate ?: date('Y-m-t'); // Default to end of current month
            
            return $this->db->fetchAll("
                SELECT 
                    a.*,
                    u.nama_lengkap as created_by_name,
                    u.role as created_by_role
                FROM absensi a
                JOIN users u ON a.created_by = u.id
                WHERE a.siswa_id = ?
                AND a.tanggal BETWEEN ? AND ?
                ORDER BY a.tanggal DESC
            ", [$siswaId, $startDate, $endDate]);
        } catch (Exception $e) {
            error_log("Error in Absensi::getAbsensiByStudent(): " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get absensi summary for a student
     */
    public function getAbsensiSummary($siswaId, $startDate = null, $endDate = null) {
        try {
            $startDate = $startDate ?: date('Y-m-01');
            $endDate = $endDate ?: date('Y-m-t');

            // Calculate total school days in the period (excluding weekends)
            $totalSchoolDays = $this->calculateSchoolDays($startDate, $endDate);

            $result = $this->db->fetch("
                SELECT
                    COUNT(*) as total_ketidakhadiran,
                    SUM(CASE WHEN jenis_ketidakhadiran = 'sakit' THEN 1 ELSE 0 END) as total_sakit,
                    SUM(CASE WHEN jenis_ketidakhadiran = 'ijin' THEN 1 ELSE 0 END) as total_ijin,
                    SUM(CASE WHEN jenis_ketidakhadiran = 'alpha' THEN 1 ELSE 0 END) as total_alpha,
                    MAX(tanggal) as ketidakhadiran_terakhir
                FROM absensi
                WHERE siswa_id = ?
                AND tanggal BETWEEN ? AND ?
            ", [$siswaId, $startDate, $endDate]);

            if ($result) {
                $totalAbsen = (int)$result['total_ketidakhadiran'];
                $totalHadir = max(0, $totalSchoolDays - $totalAbsen);
                $persentaseKehadiran = $totalSchoolDays > 0 ? ($totalHadir / $totalSchoolDays) * 100 : 100;

                $result['total_hadir'] = $totalHadir;
                $result['total_hari_sekolah'] = $totalSchoolDays;
                $result['persentase_kehadiran'] = round($persentaseKehadiran, 1);

                return $result;
            }

            return [
                'total_ketidakhadiran' => 0,
                'total_sakit' => 0,
                'total_ijin' => 0,
                'total_alpha' => 0,
                'total_hadir' => $totalSchoolDays,
                'total_hari_sekolah' => $totalSchoolDays,
                'persentase_kehadiran' => 100.0,
                'ketidakhadiran_terakhir' => null
            ];
        } catch (Exception $e) {
            error_log("Error in Absensi::getAbsensiSummary(): " . $e->getMessage());
            return [
                'total_ketidakhadiran' => 0,
                'total_sakit' => 0,
                'total_ijin' => 0,
                'total_alpha' => 0,
                'total_hadir' => 0,
                'total_hari_sekolah' => 0,
                'persentase_kehadiran' => 100.0,
                'ketidakhadiran_terakhir' => null
            ];
        }
    }

    /**
     * Calculate school days (excluding weekends) between two dates
     */
    private function calculateSchoolDays($startDate, $endDate) {
        $start = new DateTime($startDate);
        $end = new DateTime($endDate);
        $schoolDays = 0;

        while ($start <= $end) {
            $dayOfWeek = $start->format('N'); // 1 = Monday, 7 = Sunday
            if ($dayOfWeek < 6) { // Monday to Friday
                $schoolDays++;
            }
            $start->add(new DateInterval('P1D'));
        }

        return $schoolDays;
    }
    
    /**
     * Add new absensi record
     */
    public function addAbsensi($data) {
        try {
            // Validate required fields
            if (empty($data['siswa_id']) || empty($data['tanggal']) || empty($data['jenis_ketidakhadiran'])) {
                return ['success' => false, 'message' => 'Data tidak lengkap'];
            }
            
            // Check if record already exists for this date
            $existing = $this->db->fetch("
                SELECT id FROM absensi 
                WHERE siswa_id = ? AND tanggal = ?
            ", [$data['siswa_id'], $data['tanggal']]);
            
            if ($existing) {
                return ['success' => false, 'message' => 'Absensi untuk tanggal ini sudah ada'];
            }
            
            // Insert new record
            $this->db->query("
                INSERT INTO absensi (
                    siswa_id, 
                    tanggal, 
                    jenis_ketidakhadiran, 
                    keterangan, 
                    surat_keterangan,
                    jam_masuk,
                    jam_keluar,
                    created_by
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ", [
                $data['siswa_id'],
                $data['tanggal'],
                $data['jenis_ketidakhadiran'],
                $data['keterangan'] ?? null,
                $data['surat_keterangan'] ?? null,
                $data['jam_masuk'] ?? null,
                $data['jam_keluar'] ?? null,
                $data['created_by']
            ]);
            
            return ['success' => true, 'message' => 'Absensi berhasil ditambahkan'];
            
        } catch (Exception $e) {
            error_log("Error in Absensi::addAbsensi(): " . $e->getMessage());
            return ['success' => false, 'message' => 'Gagal menambahkan absensi: ' . $e->getMessage()];
        }
    }
    
    /**
     * Update absensi record
     */
    public function updateAbsensi($id, $data) {
        try {
            // Check if record exists
            $existing = $this->db->fetch("SELECT * FROM absensi WHERE id = ?", [$id]);
            if (!$existing) {
                return ['success' => false, 'message' => 'Data absensi tidak ditemukan'];
            }
            
            // Build update query
            $updateFields = [];
            $updateValues = [];
            
            if (isset($data['jenis_ketidakhadiran'])) {
                $updateFields[] = 'jenis_ketidakhadiran = ?';
                $updateValues[] = $data['jenis_ketidakhadiran'];
            }
            
            if (isset($data['keterangan'])) {
                $updateFields[] = 'keterangan = ?';
                $updateValues[] = $data['keterangan'];
            }
            
            if (isset($data['surat_keterangan'])) {
                $updateFields[] = 'surat_keterangan = ?';
                $updateValues[] = $data['surat_keterangan'];
            }
            
            if (isset($data['jam_masuk'])) {
                $updateFields[] = 'jam_masuk = ?';
                $updateValues[] = $data['jam_masuk'];
            }
            
            if (isset($data['jam_keluar'])) {
                $updateFields[] = 'jam_keluar = ?';
                $updateValues[] = $data['jam_keluar'];
            }
            
            if (empty($updateFields)) {
                return ['success' => false, 'message' => 'Tidak ada data yang diupdate'];
            }
            
            $updateFields[] = 'updated_at = NOW()';
            $updateValues[] = $id;
            
            $sql = "UPDATE absensi SET " . implode(', ', $updateFields) . " WHERE id = ?";
            $this->db->query($sql, $updateValues);
            
            return ['success' => true, 'message' => 'Absensi berhasil diupdate'];
            
        } catch (Exception $e) {
            error_log("Error in Absensi::updateAbsensi(): " . $e->getMessage());
            return ['success' => false, 'message' => 'Gagal mengupdate absensi: ' . $e->getMessage()];
        }
    }
    
    /**
     * Delete absensi record
     */
    public function deleteAbsensi($id) {
        try {
            // Check if record exists
            $existing = $this->db->fetch("SELECT * FROM absensi WHERE id = ?", [$id]);
            if (!$existing) {
                return ['success' => false, 'message' => 'Data absensi tidak ditemukan'];
            }
            
            $this->db->query("DELETE FROM absensi WHERE id = ?", [$id]);
            
            return ['success' => true, 'message' => 'Absensi berhasil dihapus'];
            
        } catch (Exception $e) {
            error_log("Error in Absensi::deleteAbsensi(): " . $e->getMessage());
            return ['success' => false, 'message' => 'Gagal menghapus absensi: ' . $e->getMessage()];
        }
    }
    
    /**
     * Get absensi by ID
     */
    public function getAbsensiById($id) {
        try {
            return $this->db->fetch("
                SELECT 
                    a.*,
                    s.nama_lengkap as nama_siswa,
                    s.nis,
                    k.nama_kelas,
                    u.nama_lengkap as created_by_name
                FROM absensi a
                JOIN siswa s ON a.siswa_id = s.id_siswa
                JOIN kelas k ON s.kelas_id = k.id
                JOIN users u ON a.created_by = u.id
                WHERE a.id = ?
            ", [$id]);
        } catch (Exception $e) {
            error_log("Error in Absensi::getAbsensiById(): " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Get monthly absensi statistics
     */
    public function getMonthlyStats($year = null, $month = null) {
        try {
            $year = $year ?: date('Y');
            $month = $month ?: date('m');
            
            return $this->db->fetchAll("
                SELECT 
                    k.nama_kelas,
                    k.tingkat,
                    COUNT(DISTINCT s.id_siswa) as total_siswa,
                    COUNT(a.id) as total_ketidakhadiran,
                    SUM(CASE WHEN a.jenis_ketidakhadiran = 'sakit' THEN 1 ELSE 0 END) as total_sakit,
                    SUM(CASE WHEN a.jenis_ketidakhadiran = 'ijin' THEN 1 ELSE 0 END) as total_ijin,
                    SUM(CASE WHEN a.jenis_ketidakhadiran = 'alpha' THEN 1 ELSE 0 END) as total_alpha
                FROM kelas k
                LEFT JOIN siswa s ON k.id = s.kelas_id AND s.status_siswa = 'aktif'
                LEFT JOIN absensi a ON s.id_siswa = a.siswa_id 
                    AND YEAR(a.tanggal) = ? 
                    AND MONTH(a.tanggal) = ?
                WHERE k.is_active = 1
                GROUP BY k.id, k.nama_kelas, k.tingkat
                ORDER BY k.tingkat, k.nama_kelas
            ", [$year, $month]);
        } catch (Exception $e) {
            error_log("Error in Absensi::getMonthlyStats(): " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get jenis ketidakhadiran options
     */
    public function getJenisKetidakhadiranOptions() {
        return [
            'sakit' => [
                'label' => 'Sakit',
                'icon' => 'bi-thermometer-half',
                'color' => 'danger',
                'description' => 'Tidak masuk karena sakit (demam, flu, dll)'
            ],
            'ijin' => [
                'label' => 'Ijin',
                'icon' => 'bi-file-text',
                'color' => 'warning',
                'description' => 'Tidak masuk dengan ijin (acara keluarga, urusan penting)'
            ],
            'alpha' => [
                'label' => 'Alpha',
                'icon' => 'bi-x-circle',
                'color' => 'secondary',
                'description' => 'Tidak masuk tanpa keterangan'
            ]
        ];
    }
    
    /**
     * Calculate attendance percentage
     */
    public function calculateAttendancePercentage($siswaId, $startDate = null, $endDate = null) {
        try {
            $startDate = $startDate ?: date('Y-m-01');
            $endDate = $endDate ?: date('Y-m-t');
            
            // Calculate total school days (excluding weekends)
            $totalDays = $this->calculateSchoolDays($startDate, $endDate);
            
            // Get absent days
            $absentDays = $this->db->fetch("
                SELECT COUNT(*) as count
                FROM absensi
                WHERE siswa_id = ?
                AND tanggal BETWEEN ? AND ?
            ", [$siswaId, $startDate, $endDate]);
            
            $absentCount = $absentDays['count'] ?? 0;
            
            if ($totalDays > 0) {
                $percentage = (($totalDays - $absentCount) / $totalDays) * 100;
                return round($percentage, 2);
            }
            
            return 100.00;
            
        } catch (Exception $e) {
            error_log("Error in Absensi::calculateAttendancePercentage(): " . $e->getMessage());
            return 100.00;
        }
    }
    

}
