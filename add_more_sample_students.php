<?php
// Script untuk menambah lebih banyak sample siswa
// File: add_more_sample_students.php

require_once __DIR__ . '/app/models/Database.php';

try {
    echo "<h2>👥 Adding More Sample Students...</h2>";
    
    $db = new Database();
    
    // Get kelas IDs
    $kelasMap = [];
    $allKelas = $db->fetchAll("SELECT id, nama_kelas FROM kelas WHERE is_active = 1");
    foreach ($allKelas as $kelas) {
        $kelasMap[$kelas['nama_kelas']] = $kelas['id'];
    }
    
    // Extended sample students for better distribution
    $additionalStudents = [
        // More KPP Students
        ['2024021', '<PERSON><PERSON> Hidayat', 'L', 'KPP-A'],
        ['2024022', '<PERSON> Safitri', 'P', 'KPP-A'],
        ['2024023', '<PERSON>dra Wijaya', 'L', 'KPP-A'],
        ['2024024', '<PERSON><PERSON>', '<PERSON>', 'KPP-A'],
        ['2024025', '<PERSON><PERSON>', '<PERSON>', 'KPP-B'],
        ['2024026', '<PERSON>ra Amelia', 'P', 'KPP-B'],
        ['2024027', '<PERSON>ang <PERSON>dhan', 'L', 'KPP-B'],
        ['2024028', 'Hani Putri', 'P', 'KPP-B'],
        ['2024029', 'Irfan Maulana', 'L', 'KPP-C'],
        ['2024030', 'Jihan Aulia', 'P', 'KPP-C'],
        ['2024031', 'Krisna Bayu', 'L', 'KPP-C'],
        ['2024032', 'Laila Sari', 'P', 'KPP-C'],
        
        // More X Students
        ['2024033', 'Muhammad Rizki', 'L', 'X-1'],
        ['2024034', 'Nadia Putri', 'P', 'X-1'],
        ['2024035', 'Omar Faruq', 'L', 'X-1'],
        ['2024036', 'Putri Ayu', 'P', 'X-1'],
        ['2024037', 'Qori Ananda', 'L', 'X-2'],
        ['2024038', 'Rani Oktavia', 'P', 'X-2'],
        ['2024039', 'Satria Budi', 'L', 'X-2'],
        ['2024040', 'Tari Wulandari', 'P', 'X-2'],
        
        // More XI Students
        ['2024041', 'Umar Fadhil', 'L', 'XI-1'],
        ['2024042', 'Vina Safira', 'P', 'XI-1'],
        ['2024043', 'Wahyu Pratama', 'L', 'XI-1'],
        ['2024044', 'Xenia Putri', 'P', 'XI-1'],
        ['2024045', 'Yoga Aditya', 'L', 'XI-2'],
        ['2024046', 'Zahra Amalia', 'P', 'XI-2'],
        ['2024047', 'Alvin Kurniawan', 'L', 'XI-2'],
        ['2024048', 'Bunga Citra', 'P', 'XI-2'],
        
        // More XII Students
        ['2024049', 'Cahyo Nugroho', 'L', 'XII-1'],
        ['2024050', 'Devi Anggraini', 'P', 'XII-1'],
        ['2024051', 'Erick Setiawan', 'L', 'XII-1'],
        ['2024052', 'Fanny Rahayu', 'P', 'XII-1'],
        ['2024053', 'Galih Pratama', 'L', 'XII-2'],
        ['2024054', 'Hesti Wulandari', 'P', 'XII-2'],
        ['2024055', 'Ivan Gunawan', 'L', 'XII-2'],
        ['2024056', 'Jessi Permata', 'P', 'XII-2'],
        
        // More KPA Students
        ['2024057', 'Kevin Adiputra', 'L', 'KPA'],
        ['2024058', 'Luna Maharani', 'P', 'KPA'],
        ['2024059', 'Mario Fernandes', 'L', 'KPA'],
        ['2024060', 'Nina Sartika', 'P', 'KPA'],
        ['2024061', 'Oscar Ramadhan', 'L', 'KPA'],
        ['2024062', 'Priska Dewi', 'P', 'KPA']
    ];
    
    $addedCount = 0;
    $existingCount = 0;
    
    foreach ($additionalStudents as $siswa) {
        if (isset($kelasMap[$siswa[3]])) {
            try {
                // Check if student already exists
                $existing = $db->fetch("SELECT id_siswa FROM siswa WHERE nis = ?", [$siswa[0]]);
                
                if (!$existing) {
                    $db->query(
                        "INSERT INTO siswa (nis, nama_lengkap, jenis_kelamin, kelas_id, tahun_masuk, status_siswa, created_by, created_at) VALUES (?, ?, ?, ?, 2024, 'aktif', 1, NOW())",
                        [$siswa[0], $siswa[1], $siswa[2], $kelasMap[$siswa[3]]]
                    );
                    echo "<p style='color: green;'>✅ Added student: {$siswa[1]} ({$siswa[0]}) - {$siswa[3]}</p>";
                    $addedCount++;
                } else {
                    echo "<p style='color: orange;'>⚠️ Student {$siswa[0]} already exists</p>";
                    $existingCount++;
                }
            } catch (Exception $e) {
                echo "<p style='color: red;'>❌ Error adding student {$siswa[0]}: " . $e->getMessage() . "</p>";
            }
        }
    }
    
    // Add some parent data for sample students
    echo "<p>Adding parent data for some students...</p>";
    
    $parentData = [
        ['2024001', 'Bapak Ahmad Yusuf', 'Guru', 'Ibu Siti Aminah', 'Ibu Rumah Tangga'],
        ['2024002', 'Bapak Hendra Gunawan', 'Pegawai Swasta', 'Ibu Dewi Sartika', 'Pedagang'],
        ['2024003', 'Bapak Bambang Sutrisno', 'Petani', 'Ibu Rina Marlina', 'Petani'],
        ['2024004', 'Bapak Agus Salim', 'Buruh', 'Ibu Maya Sari', 'Buruh'],
        ['2024005', 'Bapak Doni Prasetyo', 'Wiraswasta', 'Ibu Lina Handayani', 'Wiraswasta'],
        ['2024021', 'Bapak Arief Hidayat Sr', 'PNS', 'Ibu Bella Safitri Sr', 'Guru'],
        ['2024033', 'Bapak Muhammad Rizki Sr', 'Dokter', 'Ibu Nadia Putri Sr', 'Perawat'],
        ['2024041', 'Bapak Umar Fadhil Sr', 'Polisi', 'Ibu Vina Safira Sr', 'Ibu Rumah Tangga'],
        ['2024049', 'Bapak Cahyo Nugroho Sr', 'Pengusaha', 'Ibu Devi Anggraini Sr', 'Akuntan'],
        ['2024057', 'Bapak Kevin Adiputra Sr', 'Pilot', 'Ibu Luna Maharani Sr', 'Pramugari']
    ];
    
    foreach ($parentData as $parent) {
        try {
            $db->query(
                "UPDATE siswa SET nama_ayah = ?, pekerjaan_ayah = ?, nama_ibu = ?, pekerjaan_ibu = ? WHERE nis = ?",
                [$parent[1], $parent[2], $parent[3], $parent[4], $parent[0]]
            );
            echo "<p style='color: blue;'>🔄 Added parent data for student {$parent[0]}</p>";
        } catch (Exception $e) {
            echo "<p style='color: orange;'>⚠️ Could not add parent data for {$parent[0]}: " . $e->getMessage() . "</p>";
        }
    }
    
    // Final verification
    echo "<h3>🔍 Final Class Distribution:</h3>";
    
    $finalDistribution = $db->fetchAll("
        SELECT 
            k.nama_kelas, 
            k.tingkat,
            COUNT(s.id_siswa) as jumlah_siswa,
            SUM(CASE WHEN s.jenis_kelamin = 'L' THEN 1 ELSE 0 END) as laki_laki,
            SUM(CASE WHEN s.jenis_kelamin = 'P' THEN 1 ELSE 0 END) as perempuan
        FROM kelas k 
        LEFT JOIN siswa s ON k.id = s.kelas_id AND s.status_siswa = 'aktif'
        WHERE k.is_active = 1
        GROUP BY k.id, k.nama_kelas, k.tingkat
        ORDER BY 
            CASE k.tingkat 
                WHEN 'KPP' THEN 1 
                WHEN 'X' THEN 2 
                WHEN 'XI' THEN 3 
                WHEN 'XII' THEN 4 
                WHEN 'KPA' THEN 5 
                ELSE 6 
            END,
            k.nama_kelas
    ");
    
    echo "<table border='1' cellpadding='10' cellspacing='0' style='border-collapse: collapse; width: 100%; margin-top: 20px;'>";
    echo "<thead style='background-color: #007bff; color: white;'>";
    echo "<tr>";
    echo "<th>Kelas</th>";
    echo "<th>Tingkat</th>";
    echo "<th>Total Siswa</th>";
    echo "<th>Laki-laki</th>";
    echo "<th>Perempuan</th>";
    echo "<th>Rasio L:P</th>";
    echo "</tr>";
    echo "</thead>";
    echo "<tbody>";
    
    $grandTotal = 0;
    $grandTotalL = 0;
    $grandTotalP = 0;
    
    foreach ($finalDistribution as $dist) {
        $total = $dist['jumlah_siswa'];
        $laki = $dist['laki_laki'];
        $perempuan = $dist['perempuan'];
        $ratio = $total > 0 ? round($laki / max($perempuan, 1), 2) : 0;
        
        $grandTotal += $total;
        $grandTotalL += $laki;
        $grandTotalP += $perempuan;
        
        echo "<tr>";
        echo "<td><strong>{$dist['nama_kelas']}</strong></td>";
        echo "<td>{$dist['tingkat']}</td>";
        echo "<td style='text-align: center;'><strong>{$total}</strong></td>";
        echo "<td style='text-align: center; color: #007bff;'>{$laki}</td>";
        echo "<td style='text-align: center; color: #dc3545;'>{$perempuan}</td>";
        echo "<td style='text-align: center;'>{$ratio}</td>";
        echo "</tr>";
    }
    
    // Grand total row
    $grandRatio = $grandTotal > 0 ? round($grandTotalL / max($grandTotalP, 1), 2) : 0;
    echo "<tr style='background-color: #f8f9fa; font-weight: bold;'>";
    echo "<td colspan='2'><strong>TOTAL</strong></td>";
    echo "<td style='text-align: center;'><strong>{$grandTotal}</strong></td>";
    echo "<td style='text-align: center; color: #007bff;'><strong>{$grandTotalL}</strong></td>";
    echo "<td style='text-align: center; color: #dc3545;'><strong>{$grandTotalP}</strong></td>";
    echo "<td style='text-align: center;'><strong>{$grandRatio}</strong></td>";
    echo "</tr>";
    
    echo "</tbody>";
    echo "</table>";
    
    // Summary
    echo "<div style='margin-top: 20px; padding: 20px; background-color: #d4edda; border: 1px solid #c3e6cb; border-radius: 5px;'>";
    echo "<h4 style='color: #155724; margin-bottom: 10px;'>📊 Summary:</h4>";
    echo "<ul style='color: #155724;'>";
    echo "<li><strong>Students Added:</strong> {$addedCount} new students</li>";
    echo "<li><strong>Already Existing:</strong> {$existingCount} students</li>";
    echo "<li><strong>Total Students:</strong> {$grandTotal} students</li>";
    echo "<li><strong>Gender Distribution:</strong> {$grandTotalL} laki-laki, {$grandTotalP} perempuan</li>";
    echo "<li><strong>Average per Class:</strong> " . round($grandTotal / 10, 1) . " students</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h2 style='color: green; margin-top: 30px;'>🎉 Sample students added successfully!</h2>";
    
} catch (Exception $e) {
    echo "<h2 style='color: red;'>❌ Error adding sample students:</h2>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 20px;
    background-color: #f8f9fa;
}

h2, h3 {
    color: #495057;
}

table {
    background-color: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border-radius: 8px;
    overflow: hidden;
}

th {
    font-weight: 600;
}

td {
    padding: 12px !important;
}

tr:nth-child(even) {
    background-color: #f8f9fa;
}

tr:hover {
    background-color: #e9ecef;
}

p {
    margin: 5px 0;
    padding: 5px 10px;
    border-radius: 4px;
}

p[style*="green"] {
    background-color: #d4edda;
    border-left: 4px solid #28a745;
}

p[style*="blue"] {
    background-color: #cce7ff;
    border-left: 4px solid #007bff;
}

p[style*="orange"] {
    background-color: #fff3cd;
    border-left: 4px solid #ffc107;
}

p[style*="red"] {
    background-color: #f8d7da;
    border-left: 4px solid #dc3545;
}
</style>
