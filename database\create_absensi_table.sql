-- Create table for student attendance/absensi
-- File: database/create_absensi_table.sql

-- 1. Create absensi table
CREATE TABLE IF NOT EXISTS absensi (
    id INT PRIMARY KEY AUTO_INCREMENT,
    siswa_id INT NOT NULL,
    tanggal DATE NOT NULL,
    jenis_ketidakhadiran ENUM('sakit', 'ijin', 'alpha') NOT NULL,
    keterangan TEXT NULL,
    surat_keterangan VARCHAR(255) NULL, -- File path for medical certificate or permission letter
    jam_masuk TIME NULL,
    jam_keluar TIME NULL,
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (siswa_id) REFERENCES siswa(id_siswa) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE RESTRICT,
    
    -- Indexes for performance
    INDEX idx_siswa_tanggal (siswa_id, tanggal),
    INDEX idx_tanggal (tanggal),
    INDEX idx_jenis (jenis_ketidakhadiran),
    INDEX idx_created_by (created_by),
    
    -- Unique constraint to prevent duplicate entries for same student on same date
    UNIQUE KEY unique_siswa_tanggal (siswa_id, tanggal)
);

-- 2. Create absensi summary view
CREATE OR REPLACE VIEW view_absensi_summary AS
SELECT 
    s.id_siswa,
    s.nis,
    s.nama_lengkap,
    k.nama_kelas,
    k.tingkat,
    COUNT(a.id) as total_ketidakhadiran,
    SUM(CASE WHEN a.jenis_ketidakhadiran = 'sakit' THEN 1 ELSE 0 END) as total_sakit,
    SUM(CASE WHEN a.jenis_ketidakhadiran = 'ijin' THEN 1 ELSE 0 END) as total_ijin,
    SUM(CASE WHEN a.jenis_ketidakhadiran = 'alpha' THEN 1 ELSE 0 END) as total_alpha,
    MAX(a.tanggal) as ketidakhadiran_terakhir
FROM siswa s
LEFT JOIN kelas k ON s.kelas_id = k.id
LEFT JOIN absensi a ON s.id_siswa = a.siswa_id
WHERE s.status_siswa = 'aktif'
GROUP BY s.id_siswa, s.nis, s.nama_lengkap, k.nama_kelas, k.tingkat;

-- 3. Create monthly absensi view
CREATE OR REPLACE VIEW view_absensi_monthly AS
SELECT 
    s.id_siswa,
    s.nis,
    s.nama_lengkap,
    k.nama_kelas,
    YEAR(a.tanggal) as tahun,
    MONTH(a.tanggal) as bulan,
    COUNT(a.id) as total_ketidakhadiran,
    SUM(CASE WHEN a.jenis_ketidakhadiran = 'sakit' THEN 1 ELSE 0 END) as sakit,
    SUM(CASE WHEN a.jenis_ketidakhadiran = 'ijin' THEN 1 ELSE 0 END) as ijin,
    SUM(CASE WHEN a.jenis_ketidakhadiran = 'alpha' THEN 1 ELSE 0 END) as alpha
FROM siswa s
LEFT JOIN kelas k ON s.kelas_id = k.id
LEFT JOIN absensi a ON s.id_siswa = a.siswa_id
WHERE s.status_siswa = 'aktif' AND a.tanggal IS NOT NULL
GROUP BY s.id_siswa, s.nis, s.nama_lengkap, k.nama_kelas, YEAR(a.tanggal), MONTH(a.tanggal);

-- 4. Create stored procedure to get student attendance
DELIMITER //
CREATE OR REPLACE PROCEDURE get_student_absensi(
    IN p_siswa_id INT,
    IN p_start_date DATE,
    IN p_end_date DATE
)
BEGIN
    SELECT 
        a.*,
        u.nama_lengkap as created_by_name,
        u.role as created_by_role
    FROM absensi a
    JOIN users u ON a.created_by = u.id
    WHERE a.siswa_id = p_siswa_id
    AND a.tanggal BETWEEN COALESCE(p_start_date, DATE_SUB(CURDATE(), INTERVAL 30 DAY)) 
                     AND COALESCE(p_end_date, CURDATE())
    ORDER BY a.tanggal DESC;
END //
DELIMITER ;

-- 5. Create function to calculate attendance percentage
DELIMITER //
CREATE OR REPLACE FUNCTION calculate_attendance_percentage(
    p_siswa_id INT,
    p_start_date DATE,
    p_end_date DATE
) RETURNS DECIMAL(5,2)
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE v_total_days INT DEFAULT 0;
    DECLARE v_absent_days INT DEFAULT 0;
    DECLARE v_percentage DECIMAL(5,2) DEFAULT 100.00;
    
    -- Calculate total school days (excluding weekends)
    SELECT COUNT(*) INTO v_total_days
    FROM (
        SELECT DATE_ADD(p_start_date, INTERVAL seq.seq DAY) as date_check
        FROM (
            SELECT 0 as seq UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9 UNION
            SELECT 10 UNION SELECT 11 UNION SELECT 12 UNION SELECT 13 UNION SELECT 14 UNION SELECT 15 UNION SELECT 16 UNION SELECT 17 UNION SELECT 18 UNION SELECT 19 UNION
            SELECT 20 UNION SELECT 21 UNION SELECT 22 UNION SELECT 23 UNION SELECT 24 UNION SELECT 25 UNION SELECT 26 UNION SELECT 27 UNION SELECT 28 UNION SELECT 29 UNION
            SELECT 30 UNION SELECT 31 UNION SELECT 32 UNION SELECT 33 UNION SELECT 34 UNION SELECT 35 UNION SELECT 36 UNION SELECT 37 UNION SELECT 38 UNION SELECT 39 UNION
            SELECT 40 UNION SELECT 41 UNION SELECT 42 UNION SELECT 43 UNION SELECT 44 UNION SELECT 45 UNION SELECT 46 UNION SELECT 47 UNION SELECT 48 UNION SELECT 49 UNION
            SELECT 50 UNION SELECT 51 UNION SELECT 52 UNION SELECT 53 UNION SELECT 54 UNION SELECT 55 UNION SELECT 56 UNION SELECT 57 UNION SELECT 58 UNION SELECT 59 UNION
            SELECT 60 UNION SELECT 61 UNION SELECT 62 UNION SELECT 63 UNION SELECT 64 UNION SELECT 65 UNION SELECT 66 UNION SELECT 67 UNION SELECT 68 UNION SELECT 69 UNION
            SELECT 70 UNION SELECT 71 UNION SELECT 72 UNION SELECT 73 UNION SELECT 74 UNION SELECT 75 UNION SELECT 76 UNION SELECT 77 UNION SELECT 78 UNION SELECT 79 UNION
            SELECT 80 UNION SELECT 81 UNION SELECT 82 UNION SELECT 83 UNION SELECT 84 UNION SELECT 85 UNION SELECT 86 UNION SELECT 87 UNION SELECT 88 UNION SELECT 89 UNION
            SELECT 90 UNION SELECT 91 UNION SELECT 92 UNION SELECT 93 UNION SELECT 94 UNION SELECT 95 UNION SELECT 96 UNION SELECT 97 UNION SELECT 98 UNION SELECT 99
        ) seq
        WHERE DATE_ADD(p_start_date, INTERVAL seq.seq DAY) <= p_end_date
        AND WEEKDAY(DATE_ADD(p_start_date, INTERVAL seq.seq DAY)) < 5 -- Monday to Friday only
    ) school_days;
    
    -- Calculate absent days
    SELECT COUNT(*) INTO v_absent_days
    FROM absensi
    WHERE siswa_id = p_siswa_id
    AND tanggal BETWEEN p_start_date AND p_end_date;
    
    -- Calculate percentage
    IF v_total_days > 0 THEN
        SET v_percentage = ((v_total_days - v_absent_days) / v_total_days) * 100;
    END IF;
    
    RETURN v_percentage;
END //
DELIMITER ;

-- 6. Insert sample absensi data
INSERT IGNORE INTO absensi (siswa_id, tanggal, jenis_ketidakhadiran, keterangan, created_by) VALUES
-- Sample data for testing
(1, '2024-12-01', 'sakit', 'Demam tinggi, istirahat di rumah', 1),
(1, '2024-12-02', 'sakit', 'Masih demam, kontrol ke dokter', 1),
(2, '2024-12-03', 'ijin', 'Acara keluarga di luar kota', 1),
(3, '2024-12-04', 'alpha', 'Tidak ada keterangan', 1),
(4, '2024-12-05', 'sakit', 'Flu dan batuk', 1),
(5, '2024-12-06', 'ijin', 'Mengurus dokumen penting', 1),
(1, '2024-12-09', 'alpha', 'Terlambat bangun, tidak masuk sekolah', 1),
(2, '2024-12-10', 'sakit', 'Sakit perut, ke puskesmas', 1),
(6, '2024-12-11', 'ijin', 'Menjenguk nenek yang sakit', 1),
(7, '2024-12-12', 'sakit', 'Pusing dan mual', 1);

-- 7. Create indexes for better performance
CREATE INDEX idx_absensi_siswa_month ON absensi(siswa_id, YEAR(tanggal), MONTH(tanggal));
CREATE INDEX idx_absensi_jenis_tanggal ON absensi(jenis_ketidakhadiran, tanggal);

-- 8. Create trigger to log absensi changes
DELIMITER //
CREATE TRIGGER tr_absensi_log AFTER INSERT ON absensi
FOR EACH ROW
BEGIN
    INSERT INTO activity_log (
        table_name, 
        record_id, 
        action_type, 
        user_id, 
        description,
        created_at
    ) VALUES (
        'absensi',
        NEW.id,
        'INSERT',
        NEW.created_by,
        CONCAT('Added absence record for student ID ', NEW.siswa_id, ' on ', NEW.tanggal, ' (', NEW.jenis_ketidakhadiran, ')'),
        NOW()
    );
END //
DELIMITER ;

-- 9. Verification queries
SELECT 'Absensi table created successfully' as status;
SELECT 'Sample data inserted' as info;
SELECT COUNT(*) as total_absensi_records FROM absensi;
SELECT jenis_ketidakhadiran, COUNT(*) as count FROM absensi GROUP BY jenis_ketidakhadiran;
