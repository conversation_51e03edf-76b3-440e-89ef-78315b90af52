<?php
require_once __DIR__ . '/Database.php';
require_once __DIR__ . '/../helpers/Security.php';

class User {
    private $db;
    
    public function __construct() {
        $this->db = new Database();
    }
    
    /**
     * Authenticate user
     */
    public function authenticate($username, $password) {
        // Rate limiting check
        if (!Security::checkRateLimit('login_' . $username, 5, 900)) { // 5 attempts in 15 minutes
            Security::logSecurityEvent('login_rate_limit_exceeded', ['username' => $username]);
            return ['success' => false, 'message' => 'Terlalu banyak percobaan login. Coba lagi dalam 15 menit.'];
        }
        
        $user = $this->getUserByUsername($username);
        
        if (!$user) {
            Security::logSecurityEvent('login_failed_user_not_found', ['username' => $username]);
            return ['success' => false, 'message' => 'Username atau password salah.'];
        }
        
        // Check if account is locked
        if ($user['locked_until'] && strtotime($user['locked_until']) > time()) {
            Security::logSecurityEvent('login_failed_account_locked', ['username' => $username]);
            return ['success' => false, 'message' => 'Akun terkunci. Coba lagi nanti.'];
        }
        
        // Check if account is active
        if (!$user['is_active']) {
            Security::logSecurityEvent('login_failed_account_inactive', ['username' => $username]);
            return ['success' => false, 'message' => 'Akun tidak aktif.'];
        }
        
        // Verify password
        if (!Security::verifyPassword($password, $user['password'])) {
            $this->incrementFailedLoginAttempts($user['id']);
            Security::logSecurityEvent('login_failed_wrong_password', ['username' => $username]);
            return ['success' => false, 'message' => 'Username atau password salah.'];
        }
        
        // Reset failed login attempts and update last login
        $this->resetFailedLoginAttempts($user['id']);
        $this->updateLastLogin($user['id']);
        
        Security::logSecurityEvent('login_success', ['user_id' => $user['id'], 'username' => $username]);
        
        return ['success' => true, 'user' => $user];
    }
    
    /**
     * Get user by username
     */
    public function getUserByUsername($username) {
        return $this->db->fetch(
            "SELECT * FROM users WHERE username = ? AND is_active = 1",
            [$username]
        );
    }
    
    /**
     * Get user by email
     */
    public function getUserByEmail($email) {
        return $this->db->fetch(
            "SELECT * FROM users WHERE email = ? AND is_active = 1",
            [$email]
        );
    }
    
    /**
     * Get user by ID
     */
    public function getUserById($id) {
        return $this->db->fetch(
            "SELECT * FROM users WHERE id = ?",
            [$id]
        );
    }
    
    /**
     * Create new user
     */
    public function createUser($data) {
        // Validate required fields
        $required = ['username', 'email', 'password', 'nama_lengkap'];
        foreach ($required as $field) {
            if (empty($data[$field])) {
                return ['success' => false, 'message' => "Field {$field} wajib diisi."];
            }
        }
        
        // Validate email
        if (!Security::validateEmail($data['email'])) {
            return ['success' => false, 'message' => 'Format email tidak valid.'];
        }
        
        // Validate password
        if (!Security::validatePassword($data['password'])) {
            return ['success' => false, 'message' => 'Password minimal 8 karakter dengan kombinasi huruf dan angka.'];
        }
        
        // Check if username already exists
        if ($this->getUserByUsername($data['username'])) {
            return ['success' => false, 'message' => 'Username sudah digunakan.'];
        }
        
        // Check if email already exists
        if ($this->getUserByEmail($data['email'])) {
            return ['success' => false, 'message' => 'Email sudah digunakan.'];
        }
        
        // Hash password
        $hashedPassword = Security::hashPassword($data['password']);

        // Set pamong_type based on role
        $pamongType = null;
        if (strpos($data['role'], 'pamong_') === 0) {
            $pamongType = str_replace('pamong_', '', $data['role']);
        }

        // Set tingkat_akses based on pamong_type
        $tingkatAkses = null;
        switch ($pamongType) {
            case 'mp':
                $tingkatAkses = json_encode(['KPP']);
                break;
            case 'mt':
                $tingkatAkses = json_encode(['X']);
                break;
            case 'mm':
                $tingkatAkses = json_encode(['XI']);
                break;
            case 'mu':
                $tingkatAkses = json_encode(['XII', 'KPA']);
                break;
        }

        // Insert user
        $this->db->query(
            "INSERT INTO users (username, email, password, role, pamong_type, nama_lengkap, tingkat_akses, is_active, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())",
            [
                $data['username'],
                $data['email'],
                $hashedPassword,
                $data['role'] ?? 'staff',
                $pamongType,
                $data['nama_lengkap'],
                $tingkatAkses,
                $data['is_active'] ?? 1
            ]
        );
        
        $userId = $this->db->lastInsertId();
        
        Security::logSecurityEvent('user_created', ['user_id' => $userId, 'username' => $data['username']]);
        
        return ['success' => true, 'user_id' => $userId];
    }
    
    /**
     * Update user
     */
    public function updateUser($id, $data) {
        $user = $this->getUserById($id);
        if (!$user) {
            return ['success' => false, 'message' => 'User tidak ditemukan.'];
        }
        
        $updateFields = [];
        $params = [];
        
        // Update username if provided
        if (!empty($data['username']) && $data['username'] !== $user['username']) {
            if ($this->getUserByUsername($data['username'])) {
                return ['success' => false, 'message' => 'Username sudah digunakan.'];
            }
            $updateFields[] = 'username = ?';
            $params[] = $data['username'];
        }
        
        // Update email if provided
        if (!empty($data['email']) && $data['email'] !== $user['email']) {
            if (!Security::validateEmail($data['email'])) {
                return ['success' => false, 'message' => 'Format email tidak valid.'];
            }
            if ($this->getUserByEmail($data['email'])) {
                return ['success' => false, 'message' => 'Email sudah digunakan.'];
            }
            $updateFields[] = 'email = ?';
            $params[] = $data['email'];
        }
        
        // Update password if provided
        if (!empty($data['password'])) {
            if (!Security::validatePassword($data['password'])) {
                return ['success' => false, 'message' => 'Password minimal 8 karakter dengan kombinasi huruf dan angka.'];
            }
            $updateFields[] = 'password = ?';
            $params[] = Security::hashPassword($data['password']);
        }
        
        // Update other fields
        $allowedFields = ['role', 'nama_lengkap', 'is_active'];
        foreach ($allowedFields as $field) {
            if (isset($data[$field])) {
                $updateFields[] = "{$field} = ?";
                $params[] = $data[$field];
            }
        }
        
        if (empty($updateFields)) {
            return ['success' => false, 'message' => 'Tidak ada data yang diupdate.'];
        }
        
        $updateFields[] = 'updated_at = NOW()';
        $params[] = $id;
        
        $this->db->query(
            "UPDATE users SET " . implode(', ', $updateFields) . " WHERE id = ?",
            $params
        );
        
        Security::logSecurityEvent('user_updated', ['user_id' => $id]);
        
        return ['success' => true];
    }
    
    /**
     * Increment failed login attempts
     */
    private function incrementFailedLoginAttempts($userId) {
        $this->db->query(
            "UPDATE users SET failed_login_attempts = failed_login_attempts + 1, 
             locked_until = CASE 
                 WHEN failed_login_attempts >= 4 THEN DATE_ADD(NOW(), INTERVAL 15 MINUTE)
                 ELSE locked_until 
             END 
             WHERE id = ?",
            [$userId]
        );
    }
    
    /**
     * Reset failed login attempts
     */
    private function resetFailedLoginAttempts($userId) {
        $this->db->query(
            "UPDATE users SET failed_login_attempts = 0, locked_until = NULL WHERE id = ?",
            [$userId]
        );
    }
    
    /**
     * Update last login
     */
    private function updateLastLogin($userId) {
        $this->db->query(
            "UPDATE users SET last_login = NOW() WHERE id = ?",
            [$userId]
        );
    }
    
    /**
     * Get all users
     */
    public function getAllUsers() {
        return $this->db->fetchAll("
            SELECT
                id,
                username,
                email,
                role,
                pamong_type,
                nama_lengkap,
                is_active,
                last_login,
                created_at,
                updated_at
            FROM users
            ORDER BY
                CASE role
                    WHEN 'admin' THEN 1
                    WHEN 'pamong_mp' THEN 2
                    WHEN 'pamong_mt' THEN 3
                    WHEN 'pamong_mm' THEN 4
                    WHEN 'pamong_mu' THEN 5
                    WHEN 'wali_kelas' THEN 6
                    WHEN 'staff' THEN 7
                    ELSE 8
                END,
                nama_lengkap
        ");
    }
    
    /**
     * Delete user
     */
    public function deleteUser($id) {
        $user = $this->getUserById($id);
        if (!$user) {
            return ['success' => false, 'message' => 'User tidak ditemukan.'];
        }
        
        $this->db->query("DELETE FROM users WHERE id = ?", [$id]);
        
        Security::logSecurityEvent('user_deleted', ['user_id' => $id, 'username' => $user['username']]);
        
        return ['success' => true];
    }

    /**
     * Get role options for dropdown
     */
    public function getRoleOptions() {
        return [
            'admin' => 'Administrator',
            'pamong_mp' => 'Pamong MP (KPP)',
            'pamong_mt' => 'Pamong MT (X)',
            'pamong_mm' => 'Pamong MM (XI)',
            'pamong_mu' => 'Pamong MU (XII, KPA)',
            'wali_kelas' => 'Wali Kelas',
            'staff' => 'Staff'
        ];
    }

    /**
     * Get role display name
     */
    public function getRoleDisplayName($role) {
        $roles = $this->getRoleOptions();
        return $roles[$role] ?? $role;
    }

    /**
     * Get user statistics
     */
    public function getUserStats() {
        try {
            $stats = $this->db->fetch("
                SELECT
                    COUNT(*) as total_users,
                    SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active_users,
                    SUM(CASE WHEN role = 'admin' THEN 1 ELSE 0 END) as admin_count,
                    SUM(CASE WHEN role LIKE 'pamong_%' THEN 1 ELSE 0 END) as pamong_count,
                    SUM(CASE WHEN role = 'wali_kelas' THEN 1 ELSE 0 END) as wali_kelas_count,
                    SUM(CASE WHEN role = 'staff' THEN 1 ELSE 0 END) as staff_count
                FROM users
            ");

            return $stats;
        } catch (Exception $e) {
            error_log("Error in User::getUserStats(): " . $e->getMessage());
            return [
                'total_users' => 0,
                'active_users' => 0,
                'admin_count' => 0,
                'pamong_count' => 0,
                'wali_kelas_count' => 0,
                'staff_count' => 0
            ];
        }
    }
}
