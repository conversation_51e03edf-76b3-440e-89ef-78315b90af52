# Security for foto siswa directory
# Only allow image files
<FilesMatch "\.(jpg|jpeg|png|gif)$">
    Order Allow,Deny
    Allow from all
</FilesMatch>

# Deny access to all other files
<FilesMatch "^(?!.*\.(jpg|jpeg|png|gif)$).*$">
    Order Deny,Allow
    Deny from all
</FilesMatch>

# Prevent execution of PHP files
<Files "*.php">
    Order Deny,Allow
    Deny from all
</Files>

# Disable directory browsing
Options -Indexes

# Set proper MIME types for images
<IfModule mod_mime.c>
    AddType image/jpeg .jpg .jpeg
    AddType image/png .png
    AddType image/gif .gif
</IfModule>

# Cache control for images
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
</IfModule>
