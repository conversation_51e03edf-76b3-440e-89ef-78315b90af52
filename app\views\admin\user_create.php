<?php
// Halaman tambah user untuk admin
require_once __DIR__ . '/../../models/User.php';
$userModel = new User();
$roleOptions = $userModel->getRoleOptions();
?>

<div class="container-fluid py-4">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body bg-primary text-white rounded">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    <i class="bi bi-person-plus-fill" style="font-size: 2.5rem;"></i>
                                </div>
                                <div>
                                    <h4 class="mb-1 fw-bold">Tambah User Baru</h4>
                                    <p class="mb-0 opacity-75">
                                        <i class="bi bi-shield-check me-1"></i>
                                        Buat akun pengguna baru untuk sistem
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 text-md-end">
                            <a href="/siswa-app/public/admin/users" class="btn btn-light btn-lg">
                                <i class="bi bi-arrow-left me-2"></i>
                                Kembali
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Messages -->
    <?php if ($success): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle me-2"></i>
            <?= htmlspecialchars($success) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle me-2"></i>
            <?= htmlspecialchars($error) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Form Section -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0 text-dark">
                        <i class="bi bi-person-plus me-2"></i>
                        Form Tambah User
                    </h5>
                </div>
                <div class="card-body p-4">
                    <form method="POST" action="/siswa-app/public/admin/users/store" id="createUserForm">
                        <input type="hidden" name="csrf_token" value="<?= $csrf_token ?>">

                        <!-- Basic Information -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="username" class="form-label fw-semibold">
                                        <i class="bi bi-person me-1"></i>
                                        Username <span class="text-danger">*</span>
                                    </label>
                                    <input type="text" 
                                           class="form-control form-control-lg" 
                                           id="username" 
                                           name="username" 
                                           placeholder="Masukkan username..."
                                           required>
                                    <div class="form-text">
                                        <i class="bi bi-info-circle me-1"></i>
                                        Username harus unik dan tidak boleh sama dengan yang lain
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="email" class="form-label fw-semibold">
                                        <i class="bi bi-envelope me-1"></i>
                                        Email <span class="text-danger">*</span>
                                    </label>
                                    <input type="email" 
                                           class="form-control form-control-lg" 
                                           id="email" 
                                           name="email" 
                                           placeholder="Masukkan email..."
                                           required>
                                    <div class="form-text">
                                        <i class="bi bi-info-circle me-1"></i>
                                        Email harus valid dan unik
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Name and Password -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="nama_lengkap" class="form-label fw-semibold">
                                        <i class="bi bi-card-text me-1"></i>
                                        Nama Lengkap <span class="text-danger">*</span>
                                    </label>
                                    <input type="text" 
                                           class="form-control form-control-lg" 
                                           id="nama_lengkap" 
                                           name="nama_lengkap" 
                                           placeholder="Masukkan nama lengkap..."
                                           required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="password" class="form-label fw-semibold">
                                        <i class="bi bi-lock me-1"></i>
                                        Password <span class="text-danger">*</span>
                                    </label>
                                    <input type="password" 
                                           class="form-control form-control-lg" 
                                           id="password" 
                                           name="password" 
                                           placeholder="Masukkan password..."
                                           required 
                                           minlength="6">
                                    <div class="form-text">
                                        <i class="bi bi-info-circle me-1"></i>
                                        Password minimal 6 karakter
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Role and Status -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="role" class="form-label fw-semibold">
                                        <i class="bi bi-shield me-1"></i>
                                        Role <span class="text-danger">*</span>
                                    </label>
                                    <select class="form-select form-select-lg" id="role" name="role" required>
                                        <option value="">Pilih Role</option>
                                        <?php foreach ($roleOptions as $value => $label): ?>
                                            <option value="<?= $value ?>"><?= $label ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                    <div class="form-text">
                                        <i class="bi bi-info-circle me-1"></i>
                                        Pilih role sesuai dengan tugas dan tanggung jawab user
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="is_active" class="form-label fw-semibold">
                                        <i class="bi bi-toggle-on me-1"></i>
                                        Status Akun
                                    </label>
                                    <div class="form-check form-switch mt-2">
                                        <input class="form-check-input" 
                                               type="checkbox" 
                                               id="is_active" 
                                               name="is_active" 
                                               checked>
                                        <label class="form-check-label" for="is_active">
                                            Akun Aktif
                                        </label>
                                    </div>
                                    <div class="form-text">
                                        <i class="bi bi-info-circle me-1"></i>
                                        Centang untuk mengaktifkan akun
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Role Information -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="card bg-light border-0">
                                    <div class="card-body">
                                        <h6 class="card-title">
                                            <i class="bi bi-info-circle me-2"></i>
                                            Informasi Role
                                        </h6>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <ul class="list-unstyled mb-0">
                                                    <li class="mb-2">
                                                        <span class="badge bg-warning text-dark me-2">Admin</span>
                                                        Akses penuh ke semua fitur sistem
                                                    </li>
                                                    <li class="mb-2">
                                                        <span class="badge bg-info me-2">Pamong MP</span>
                                                        Akses siswa KPP saja
                                                    </li>
                                                    <li class="mb-2">
                                                        <span class="badge bg-info me-2">Pamong MT</span>
                                                        Akses siswa X saja
                                                    </li>
                                                </ul>
                                            </div>
                                            <div class="col-md-6">
                                                <ul class="list-unstyled mb-0">
                                                    <li class="mb-2">
                                                        <span class="badge bg-info me-2">Pamong MM</span>
                                                        Akses siswa XI saja
                                                    </li>
                                                    <li class="mb-2">
                                                        <span class="badge bg-info me-2">Pamong MU</span>
                                                        Akses siswa XII & KPA
                                                    </li>
                                                    <li class="mb-2">
                                                        <span class="badge bg-secondary me-2">Wali Kelas</span>
                                                        Akses siswa di kelasnya saja
                                                    </li>
                                                    <li class="mb-2">
                                                        <span class="badge bg-dark me-2">Staff</span>
                                                        Akses read-only ke semua siswa
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-between align-items-center pt-3 border-top">
                                    <div>
                                        <a href="/siswa-app/public/admin/users" class="btn btn-outline-secondary btn-lg">
                                            <i class="bi bi-x-circle me-2"></i>
                                            Batal
                                        </a>
                                    </div>
                                    <div>
                                        <button type="submit" class="btn btn-primary btn-lg px-5" id="submitBtn">
                                            <i class="bi bi-person-plus me-2"></i>
                                            Buat User
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Custom styling for create user page */
.form-control, .form-select {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.15);
    transform: translateY(-1px);
}

.form-control-lg, .form-select-lg {
    padding: 0.75rem 1rem;
    font-size: 1.1rem;
}

.form-label {
    color: #495057;
    margin-bottom: 0.75rem;
}

.form-label i {
    color: #007bff;
}

.card {
    border-radius: 12px;
    overflow: hidden;
}

.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.btn-lg {
    padding: 0.75rem 2rem;
}

/* Form group spacing */
.form-group {
    margin-bottom: 1.5rem;
}

/* Badge styling */
.badge {
    font-size: 0.75rem;
    font-weight: 500;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .container-fluid {
        padding: 1rem;
    }
    
    .card-body {
        padding: 1.5rem !important;
    }
    
    .btn-lg {
        padding: 0.5rem 1.5rem;
        font-size: 1rem;
    }
    
    .form-control-lg, .form-select-lg {
        padding: 0.5rem 0.75rem;
        font-size: 1rem;
    }
}

/* Loading state */
.btn.loading {
    pointer-events: none;
    opacity: 0.7;
}

.btn.loading i {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
</style>

<script>
// Form validation and enhancement
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('createUserForm');
    const submitBtn = document.getElementById('submitBtn');
    
    // Form submission handling
    form.addEventListener('submit', function(e) {
        // Show loading state
        submitBtn.classList.add('loading');
        submitBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>Membuat User...';
        submitBtn.disabled = true;
    });
    
    // Username validation
    const usernameInput = document.getElementById('username');
    usernameInput.addEventListener('input', function() {
        // Remove spaces and convert to lowercase
        this.value = this.value.replace(/\s/g, '').toLowerCase();
    });
    
    // Email validation
    const emailInput = document.getElementById('email');
    emailInput.addEventListener('blur', function() {
        const email = this.value;
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        
        if (email && !emailRegex.test(email)) {
            this.classList.add('is-invalid');
        } else {
            this.classList.remove('is-invalid');
        }
    });
    
    // Password strength indicator
    const passwordInput = document.getElementById('password');
    passwordInput.addEventListener('input', function() {
        const password = this.value;
        const strength = getPasswordStrength(password);
        
        // Remove existing classes
        this.classList.remove('is-valid', 'is-invalid');
        
        if (password.length > 0) {
            if (strength >= 3) {
                this.classList.add('is-valid');
            } else if (password.length >= 6) {
                // Minimum length met but weak
                this.classList.add('is-valid');
            } else {
                this.classList.add('is-invalid');
            }
        }
    });
    
    function getPasswordStrength(password) {
        let strength = 0;
        if (password.length >= 6) strength++;
        if (/[a-z]/.test(password)) strength++;
        if (/[A-Z]/.test(password)) strength++;
        if (/[0-9]/.test(password)) strength++;
        if (/[^A-Za-z0-9]/.test(password)) strength++;
        return strength;
    }
});
</script>
