<?php
/**
 * <PERSON>ript untuk mengupdate kolom tingkat dari INT ke VARCHAR
 */

require_once 'app/config/db.php';

try {
    $pdo = getDBConnection();
    
    echo "=== UPDATE TINGKAT COLUMN ===\n";
    
    // Check current column type
    $columnInfo = $pdo->query("DESCRIBE kelas tingkat")->fetch();
    echo "Current tingkat column type: " . $columnInfo['Type'] . "\n";
    
    // Check if we need to update
    if (strpos($columnInfo['Type'], 'varchar') !== false) {
        echo "Column tingkat is already VARCHAR. No update needed.\n";
    } else {
        echo "Updating tingkat column from INT to VARCHAR...\n";
        
        // First, let's see what data we have
        $existingData = $pdo->query("SELECT DISTINCT tingkat FROM kelas ORDER BY tingkat")->fetchAll();
        echo "Existing tingkat values:\n";
        foreach ($existingData as $row) {
            echo "- " . $row['tingkat'] . "\n";
        }
        
        // Update the column type
        $pdo->exec("ALTER TABLE kelas MODIFY COLUMN tingkat VARCHAR(10) NOT NULL");
        echo "✓ Column tingkat updated to VARCHAR(10)\n";
        
        // Update existing numeric values to new format
        $updates = [
            '10' => 'X',
            '11' => 'XI', 
            '12' => 'XII'
        ];
        
        foreach ($updates as $oldValue => $newValue) {
            $stmt = $pdo->prepare("UPDATE kelas SET tingkat = ? WHERE tingkat = ?");
            $result = $stmt->execute([$newValue, $oldValue]);
            if ($result) {
                $affected = $stmt->rowCount();
                if ($affected > 0) {
                    echo "✓ Updated $affected records from tingkat '$oldValue' to '$newValue'\n";
                }
            }
        }
    }
    
    // Update indexes if needed
    echo "\nUpdating indexes...\n";
    try {
        $pdo->exec("DROP INDEX idx_tingkat ON kelas");
        echo "✓ Dropped old index idx_tingkat\n";
    } catch (Exception $e) {
        echo "- Index idx_tingkat doesn't exist or already dropped\n";
    }
    
    try {
        $pdo->exec("CREATE INDEX idx_tingkat ON kelas(tingkat)");
        echo "✓ Created new index idx_tingkat\n";
    } catch (Exception $e) {
        echo "- Index idx_tingkat already exists\n";
    }
    
    // Show final data
    echo "\n=== FINAL DATA ===\n";
    $finalData = $pdo->query("
        SELECT tingkat, COUNT(*) as count 
        FROM kelas 
        WHERE is_active = 1 
        GROUP BY tingkat 
        ORDER BY 
            CASE tingkat 
                WHEN 'KPP' THEN 1
                WHEN 'X' THEN 2
                WHEN 'XI' THEN 3
                WHEN 'XII' THEN 4
                WHEN 'KPA' THEN 5
                ELSE 6
            END
    ")->fetchAll();
    
    $tingkatLabels = [
        'KPP' => 'KPP (Kelas Persiapan Program)',
        'X' => 'Kelas X (Kelas 10)',
        'XI' => 'Kelas XI (Kelas 11)',
        'XII' => 'Kelas XII (Kelas 12)',
        'KPA' => 'KPA (Kelas Program Akselerasi)'
    ];
    
    foreach ($finalData as $row) {
        $tingkat = $row['tingkat'];
        $label = $tingkatLabels[$tingkat] ?? $tingkat;
        echo "- $label: " . $row['count'] . " kelas\n";
    }
    
    // Verify column structure
    echo "\n=== COLUMN VERIFICATION ===\n";
    $newColumnInfo = $pdo->query("DESCRIBE kelas tingkat")->fetch();
    echo "New tingkat column type: " . $newColumnInfo['Type'] . "\n";
    echo "Null: " . $newColumnInfo['Null'] . "\n";
    echo "Default: " . ($newColumnInfo['Default'] ?? 'NULL') . "\n";
    
    echo "\n✅ Update completed successfully!\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Pastikan database sudah dikonfigurasi dengan benar.\n";
}
?>
