<?php
/**
 * Script untuk membuat user admin
 */

require_once 'app/config/db.php';

try {
    $pdo = getDBConnection();
    
    // Check if users table exists
    $checkTable = $pdo->query("SHOW TABLES LIKE 'users'");
    if ($checkTable->rowCount() == 0) {
        echo "Tabel users tidak ditemukan. Pastikan database sudah diimport.\n";
        exit;
    }
    
    // Check if admin user already exists
    $stmt = $pdo->prepare("SELECT * FROM users WHERE username = 'admin' OR role = 'admin'");
    $stmt->execute();
    $existingAdmin = $stmt->fetch();
    
    if ($existingAdmin) {
        echo "User admin sudah ada:\n";
        echo "- Username: " . $existingAdmin['username'] . "\n";
        echo "- Email: " . $existingAdmin['email'] . "\n";
        echo "- Nama: " . $existingAdmin['nama_lengkap'] . "\n";
        echo "- Status: " . ($existingAdmin['is_active'] ? 'Aktif' : 'Tidak Aktif') . "\n";
        echo "- Dibuat: " . $existingAdmin['created_at'] . "\n\n";
        
        // Update password if needed
        $newPassword = 'admin123';
        $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
        
        $updateStmt = $pdo->prepare("UPDATE users SET password = ?, is_active = 1 WHERE id = ?");
        $result = $updateStmt->execute([$hashedPassword, $existingAdmin['id']]);
        
        if ($result) {
            echo "✓ Password admin berhasil diperbarui.\n";
            echo "Login credentials:\n";
            echo "- Username: " . $existingAdmin['username'] . "\n";
            echo "- Password: $newPassword\n";
        }
    } else {
        // Create new admin user
        $adminData = [
            'username' => 'admin',
            'email' => '<EMAIL>',
            'password' => password_hash('admin123', PASSWORD_DEFAULT),
            'role' => 'admin',
            'nama_lengkap' => 'Administrator Sistem',
            'is_active' => 1
        ];
        
        $insertStmt = $pdo->prepare("
            INSERT INTO users (username, email, password, role, nama_lengkap, is_active, created_at) 
            VALUES (?, ?, ?, ?, ?, ?, NOW())
        ");
        
        $result = $insertStmt->execute([
            $adminData['username'],
            $adminData['email'],
            $adminData['password'],
            $adminData['role'],
            $adminData['nama_lengkap'],
            $adminData['is_active']
        ]);
        
        if ($result) {
            echo "✓ User admin berhasil dibuat!\n\n";
            echo "Login credentials:\n";
            echo "- Username: admin\n";
            echo "- Password: admin123\n";
            echo "- Email: <EMAIL>\n";
        } else {
            echo "✗ Gagal membuat user admin.\n";
        }
    }
    
    // Show all users
    echo "\n=== DAFTAR SEMUA USER ===\n";
    $allUsers = $pdo->query("SELECT username, email, role, nama_lengkap, is_active, created_at FROM users ORDER BY created_at")->fetchAll();
    
    foreach ($allUsers as $user) {
        $status = $user['is_active'] ? 'Aktif' : 'Tidak Aktif';
        echo "- {$user['username']} ({$user['role']}) - {$user['nama_lengkap']} - $status\n";
    }
    
    echo "\nTotal users: " . count($allUsers) . "\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Pastikan database sudah dikonfigurasi dengan benar.\n";
}
?>
