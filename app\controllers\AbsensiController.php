<?php
require_once __DIR__ . '/../models/Absensi.php';
require_once __DIR__ . '/../models/Siswa.php';
require_once __DIR__ . '/../helpers/Security.php';

class AbsensiController {
    private $absensiModel;
    private $siswaModel;
    
    public function __construct() {
        $this->absensiModel = new Absensi();
        $this->siswaModel = new Siswa();
    }
    
    /**
     * Show add absensi form
     */
    public function add($siswaId) {
        Security::requireAuth();
        
        // Check if user can access this student
        Security::requireAccessToSiswa($siswaId);
        
        // Check if user can edit (staff cannot edit)
        if (!Security::canEditSiswa($siswaId)) {
            $_SESSION['error'] = 'Anda tidak memiliki akses untuk menambah absensi';
            header('Location: /siswa-app/public/siswa/detail/' . $siswaId);
            exit;
        }
        
        $siswa = $this->siswaModel->getById($siswaId);
        if (!$siswa) {
            $_SESSION['error'] = 'Siswa tidak ditemukan';
            header('Location: /siswa-app/public/siswa');
            exit;
        }
        
        $data = [
            'title' => 'Tambah Absensi - ' . $siswa['nama_lengkap'],
            'siswa' => $siswa,
            'jenis_options' => $this->absensiModel->getJenisKetidakhadiranOptions(),
            'csrf_token' => Security::generateCSRFToken(),
            'success' => $_SESSION['absensi_success'] ?? null,
            'error' => $_SESSION['absensi_error'] ?? null
        ];
        
        // Clear messages
        unset($_SESSION['absensi_success'], $_SESSION['absensi_error']);
        
        $this->view('absensi/add', $data);
    }
    
    /**
     * Process add absensi
     */
    public function create() {
        Security::requireAuth();
        
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Location: /siswa-app/public/siswa');
            exit;
        }
        
        try {
            // Validate CSRF token
            if (!Security::validateCSRFToken($_POST['csrf_token'] ?? '')) {
                throw new Exception('Token keamanan tidak valid');
            }
            
            $siswaId = Security::sanitizeInput($_POST['siswa_id'] ?? '');
            
            // Check access
            Security::requireAccessToSiswa($siswaId);
            if (!Security::canEditSiswa($siswaId)) {
                throw new Exception('Anda tidak memiliki akses untuk menambah absensi');
            }
            
            // Get form data
            $data = [
                'siswa_id' => $siswaId,
                'tanggal' => $_POST['tanggal'] ?? '',
                'jenis_ketidakhadiran' => $_POST['jenis_ketidakhadiran'] ?? '',
                'keterangan' => Security::sanitizeInput($_POST['keterangan'] ?? ''),
                'jam_masuk' => $_POST['jam_masuk'] ?? null,
                'jam_keluar' => $_POST['jam_keluar'] ?? null,
                'created_by' => $_SESSION['user_id']
            ];
            
            // Validate required fields
            if (empty($data['siswa_id'])) {
                throw new Exception('Siswa ID harus diisi');
            }
            if (empty($data['tanggal'])) {
                throw new Exception('Tanggal harus diisi');
            }
            if (empty($data['jenis_ketidakhadiran'])) {
                throw new Exception('Jenis ketidakhadiran harus dipilih');
            }
            
            // Validate date
            $tanggal = DateTime::createFromFormat('Y-m-d', $data['tanggal']);
            if (!$tanggal) {
                throw new Exception('Format tanggal tidak valid');
            }
            
            // Check if date is not in the future
            if ($tanggal > new DateTime()) {
                throw new Exception('Tanggal tidak boleh di masa depan');
            }
            
            // Handle file upload for surat keterangan
            if (isset($_FILES['surat_keterangan']) && $_FILES['surat_keterangan']['error'] === UPLOAD_ERR_OK) {
                $uploadResult = $this->handleFileUpload($_FILES['surat_keterangan']);
                if ($uploadResult['success']) {
                    $data['surat_keterangan'] = $uploadResult['filename'];
                } else {
                    throw new Exception($uploadResult['message']);
                }
            }
            
            // Add absensi
            $result = $this->absensiModel->addAbsensi($data);
            
            if ($result['success']) {
                $_SESSION['success'] = 'Absensi berhasil ditambahkan';
                header('Location: /siswa-app/public/siswa/detail/' . $siswaId);
            } else {
                throw new Exception($result['message']);
            }
            
        } catch (Exception $e) {
            $_SESSION['absensi_error'] = $e->getMessage();
            $siswaId = $_POST['siswa_id'] ?? '';
            if ($siswaId) {
                header('Location: /siswa-app/public/absensi/add/' . $siswaId);
            } else {
                header('Location: /siswa-app/public/siswa');
            }
        }
        exit;
    }
    
    /**
     * Show edit absensi form
     */
    public function edit($id) {
        Security::requireAuth();
        
        $absensi = $this->absensiModel->getAbsensiById($id);
        if (!$absensi) {
            $_SESSION['error'] = 'Data absensi tidak ditemukan';
            header('Location: /siswa-app/public/siswa');
            exit;
        }
        
        // Check access
        Security::requireAccessToSiswa($absensi['siswa_id']);
        if (!Security::canEditSiswa($absensi['siswa_id'])) {
            $_SESSION['error'] = 'Anda tidak memiliki akses untuk mengedit absensi';
            header('Location: /siswa-app/public/siswa/detail/' . $absensi['siswa_id']);
            exit;
        }
        
        $data = [
            'title' => 'Edit Absensi - ' . $absensi['nama_siswa'],
            'absensi' => $absensi,
            'jenis_options' => $this->absensiModel->getJenisKetidakhadiranOptions(),
            'csrf_token' => Security::generateCSRFToken(),
            'success' => $_SESSION['absensi_success'] ?? null,
            'error' => $_SESSION['absensi_error'] ?? null
        ];
        
        // Clear messages
        unset($_SESSION['absensi_success'], $_SESSION['absensi_error']);
        
        $this->view('absensi/edit', $data);
    }
    
    /**
     * Process update absensi
     */
    public function update($id) {
        Security::requireAuth();
        
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Location: /siswa-app/public/absensi/edit/' . $id);
            exit;
        }
        
        try {
            // Validate CSRF token
            if (!Security::validateCSRFToken($_POST['csrf_token'] ?? '')) {
                throw new Exception('Token keamanan tidak valid');
            }
            
            $absensi = $this->absensiModel->getAbsensiById($id);
            if (!$absensi) {
                throw new Exception('Data absensi tidak ditemukan');
            }
            
            // Check access
            Security::requireAccessToSiswa($absensi['siswa_id']);
            if (!Security::canEditSiswa($absensi['siswa_id'])) {
                throw new Exception('Anda tidak memiliki akses untuk mengedit absensi');
            }
            
            // Get form data
            $data = [
                'jenis_ketidakhadiran' => $_POST['jenis_ketidakhadiran'] ?? '',
                'keterangan' => Security::sanitizeInput($_POST['keterangan'] ?? ''),
                'jam_masuk' => $_POST['jam_masuk'] ?? null,
                'jam_keluar' => $_POST['jam_keluar'] ?? null
            ];
            
            // Validate required fields
            if (empty($data['jenis_ketidakhadiran'])) {
                throw new Exception('Jenis ketidakhadiran harus dipilih');
            }
            
            // Handle file upload for surat keterangan
            if (isset($_FILES['surat_keterangan']) && $_FILES['surat_keterangan']['error'] === UPLOAD_ERR_OK) {
                $uploadResult = $this->handleFileUpload($_FILES['surat_keterangan']);
                if ($uploadResult['success']) {
                    // Delete old file if exists
                    if ($absensi['surat_keterangan']) {
                        $oldFile = __DIR__ . '/../../uploads/surat_keterangan/' . $absensi['surat_keterangan'];
                        if (file_exists($oldFile)) {
                            unlink($oldFile);
                        }
                    }
                    $data['surat_keterangan'] = $uploadResult['filename'];
                } else {
                    throw new Exception($uploadResult['message']);
                }
            }
            
            // Update absensi
            $result = $this->absensiModel->updateAbsensi($id, $data);
            
            if ($result['success']) {
                $_SESSION['success'] = 'Absensi berhasil diupdate';
                header('Location: /siswa-app/public/siswa/detail/' . $absensi['siswa_id']);
            } else {
                throw new Exception($result['message']);
            }
            
        } catch (Exception $e) {
            $_SESSION['absensi_error'] = $e->getMessage();
            header('Location: /siswa-app/public/absensi/edit/' . $id);
        }
        exit;
    }
    
    /**
     * Delete absensi
     */
    public function delete($id) {
        Security::requireAuth();
        
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Location: /siswa-app/public/siswa');
            exit;
        }
        
        try {
            // Validate CSRF token
            if (!Security::validateCSRFToken($_POST['csrf_token'] ?? '')) {
                throw new Exception('Token keamanan tidak valid');
            }
            
            $absensi = $this->absensiModel->getAbsensiById($id);
            if (!$absensi) {
                throw new Exception('Data absensi tidak ditemukan');
            }
            
            // Check access
            Security::requireAccessToSiswa($absensi['siswa_id']);
            if (!Security::canEditSiswa($absensi['siswa_id'])) {
                throw new Exception('Anda tidak memiliki akses untuk menghapus absensi');
            }
            
            // Delete file if exists
            if ($absensi['surat_keterangan']) {
                $file = __DIR__ . '/../../uploads/surat_keterangan/' . $absensi['surat_keterangan'];
                if (file_exists($file)) {
                    unlink($file);
                }
            }
            
            // Delete absensi
            $result = $this->absensiModel->deleteAbsensi($id);
            
            if ($result['success']) {
                $_SESSION['success'] = 'Absensi berhasil dihapus';
            } else {
                throw new Exception($result['message']);
            }
            
        } catch (Exception $e) {
            $_SESSION['error'] = $e->getMessage();
        }
        
        // Redirect back to student detail
        $siswaId = $absensi['siswa_id'] ?? '';
        if ($siswaId) {
            header('Location: /siswa-app/public/siswa/detail/' . $siswaId);
        } else {
            header('Location: /siswa-app/public/siswa');
        }
        exit;
    }
    
    /**
     * Handle file upload for surat keterangan
     */
    private function handleFileUpload($file) {
        try {
            // Create upload directory if not exists
            $uploadDir = __DIR__ . '/../../uploads/surat_keterangan/';
            if (!is_dir($uploadDir)) {
                mkdir($uploadDir, 0755, true);
            }
            
            // Validate file
            $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'application/pdf'];
            $maxSize = 5 * 1024 * 1024; // 5MB
            
            if (!in_array($file['type'], $allowedTypes)) {
                return ['success' => false, 'message' => 'Tipe file tidak diizinkan. Gunakan JPG, PNG, GIF, atau PDF'];
            }
            
            if ($file['size'] > $maxSize) {
                return ['success' => false, 'message' => 'Ukuran file terlalu besar. Maksimal 5MB'];
            }
            
            // Generate unique filename
            $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
            $filename = 'surat_' . date('YmdHis') . '_' . uniqid() . '.' . $extension;
            $filepath = $uploadDir . $filename;
            
            // Move uploaded file
            if (move_uploaded_file($file['tmp_name'], $filepath)) {
                return ['success' => true, 'filename' => $filename];
            } else {
                return ['success' => false, 'message' => 'Gagal mengupload file'];
            }
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Error upload: ' . $e->getMessage()];
        }
    }
    
    /**
     * Render view
     */
    private function view($path, $data = []) {
        extract($data);
        require_once __DIR__ . '/../views/layouts/header.php';
        require_once __DIR__ . '/../views/' . $path . '.php';
        require_once __DIR__ . '/../views/layouts/footer.php';
    }
}
