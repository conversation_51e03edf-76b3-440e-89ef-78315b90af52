<?php
require_once __DIR__ . '/../models/Siswa.php';
require_once __DIR__ . '/../models/Kelas.php';
require_once __DIR__ . '/../helpers/Security.php';

class SiswaController {
    private $siswa;
    private $kelas;

    public function __construct() {
        $this->siswa = new Siswa();
        $this->kelas = new Kelas();
    }

    public function index() {
        $data['siswa'] = $this->siswa->getAll();
        $this->view('siswa/list', $data);
    }

    public function create() {
        $data['kelas'] = $this->kelas->getAll();
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $nama = $_POST['nama'] ?? '';
            $kelas_id = $_POST['kelas_id'] ?? 0;
            if ($nama && $kelas_id) {
                $this->siswa->create($nama, $kelas_id);
                header('Location: /siswa');
                exit;
            }
        }
        $this->view('siswa/form', $data);
    }

    public function edit($id) {
        Security::requireAuth();

        // Check if user can edit this student
        Security::requireEditSiswa($id);

        // Get student data
        $siswa = $this->siswa->getById($id);
        if (!$siswa) {
            $_SESSION['error'] = 'Data siswa tidak ditemukan';
            header('Location: /siswa-app/public/siswa');
            exit;
        }

        // Handle form submission
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            try {
                // Validate CSRF token
                if (!Security::validateCSRFToken($_POST['csrf_token'] ?? '')) {
                    throw new Exception('Token keamanan tidak valid');
                }

                // Get form data
                $updateData = [
                    'nis' => $_POST['nis'] ?? '',
                    'nisn' => $_POST['nisn'] ?? '',
                    'nama_lengkap' => $_POST['nama_lengkap'] ?? '',
                    'jenis_kelamin' => $_POST['jenis_kelamin'] ?? '',
                    'tempat_lahir' => $_POST['tempat_lahir'] ?? '',
                    'tanggal_lahir' => $_POST['tanggal_lahir'] ?? '',
                    'kelas_id' => $_POST['kelas_id'] ?? '',
                    'tahun_masuk' => $_POST['tahun_masuk'] ?? '',
                    'status_siswa' => $_POST['status_siswa'] ?? 'Aktif',
                    'email' => $_POST['email'] ?? '',
                    'no_telepon' => $_POST['no_telepon'] ?? '',
                    'alamat' => $_POST['alamat'] ?? ''
                ];

                // Validate required fields
                if (empty($updateData['nis'])) {
                    throw new Exception('NIS harus diisi');
                }
                if (empty($updateData['nama_lengkap'])) {
                    throw new Exception('Nama lengkap harus diisi');
                }
                if (empty($updateData['jenis_kelamin'])) {
                    throw new Exception('Jenis kelamin harus dipilih');
                }
                if (empty($updateData['kelas_id'])) {
                    throw new Exception('Kelas harus dipilih');
                }

                // Update student data
                $result = $this->siswa->updateComplete($id, $updateData);

                if ($result) {
                    $_SESSION['success'] = 'Data siswa berhasil diperbarui';
                    header('Location: /siswa-app/public/siswa/detail/' . $id);
                    exit;
                } else {
                    throw new Exception('Gagal memperbarui data siswa');
                }

            } catch (Exception $e) {
                $_SESSION['error'] = $e->getMessage();
            }
        }

        // Get kelas list for dropdown
        $kelasList = $this->kelas->getAll();

        $data = [
            'title' => 'Edit Siswa - ' . $siswa['nama_lengkap'],
            'siswa' => $siswa,
            'kelas_list' => $kelasList,
            'csrf_token' => Security::generateCSRFToken()
        ];

        $this->view('siswa/edit', $data);
    }

    public function delete($id) {
        $this->siswa->delete($id);
        header('Location: /siswa');
        exit;
    }

    public function detail($id) {
        Security::requireAuth();

        // Check if user can access this student
        Security::requireAccessToSiswa($id);

        $siswa = $this->siswa->getById($id);
        if (!$siswa) {
            $_SESSION['error'] = 'Data siswa tidak ditemukan';
            header('Location: /siswa-app/public/siswa');
            exit;
        }

        // Get berkas for this student
        require_once __DIR__ . '/../models/Berkas.php';
        $berkasModel = new Berkas();
        $berkas = $berkasModel->getBySiswaId($id);

        // Get catatan for this student
        require_once __DIR__ . '/../models/CatatanSiswa.php';
        $catatanModel = new CatatanSiswa();
        $catatanGrouped = $catatanModel->getGroupedBySiswaId($id);
        $catatanCategories = $catatanModel->getCategoriesGrouped();

        $data = [
            'title' => 'Detail Siswa - ' . $siswa['nama_lengkap'],
            'siswa' => $siswa,
            'berkas' => $berkas,
            'catatan_grouped' => $catatanGrouped,
            'catatan_categories' => $catatanCategories,
            'csrf_token' => Security::generateCSRFToken()
        ];

        $this->view('siswa/detail_new', $data);
    }

    /**
     * Upload foto siswa
     */
    public function uploadFoto($id) {
        Security::requireAuth();

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $_SESSION['error'] = 'Method tidak diizinkan';
            header('Location: /siswa-app/public/siswa/detail/' . $id);
            exit;
        }

        // Verify CSRF token
        $csrfToken = $_POST['csrf_token'] ?? '';
        if (!Security::verifyCSRFToken($csrfToken)) {
            $_SESSION['error'] = 'Token keamanan tidak valid';
            header('Location: /siswa-app/public/siswa/detail/' . $id);
            exit;
        }

        // Get student data
        $siswa = $this->siswa->getById($id);
        if (!$siswa) {
            $_SESSION['error'] = 'Data siswa tidak ditemukan';
            header('Location: /siswa-app/public/siswa');
            exit;
        }

        try {
            // Validate file upload
            if (!isset($_FILES['foto_siswa']) || $_FILES['foto_siswa']['error'] !== UPLOAD_ERR_OK) {
                throw new Exception('File foto tidak valid atau gagal diupload');
            }

            $file = $_FILES['foto_siswa'];

            // Validate file type
            $allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
            $fileType = strtolower($file['type']);
            if (!in_array($fileType, $allowedTypes)) {
                throw new Exception('Format file tidak didukung. Gunakan JPG, JPEG, PNG, atau GIF');
            }

            // Validate file size (2MB)
            $maxSize = 2 * 1024 * 1024; // 2MB
            if ($file['size'] > $maxSize) {
                throw new Exception('Ukuran file terlalu besar. Maksimal 2MB');
            }

            // Create upload directory if not exists
            $uploadDir = __DIR__ . '/../../public/uploads/foto_siswa/';
            if (!is_dir($uploadDir)) {
                mkdir($uploadDir, 0755, true);
            }

            // Generate unique filename
            $fileExtension = pathinfo($file['name'], PATHINFO_EXTENSION);
            $fileName = 'siswa_' . $id . '_' . time() . '.' . $fileExtension;
            $filePath = $uploadDir . $fileName;

            // Delete old photo if exists
            if (!empty($siswa['foto'])) {
                $oldPhotoPath = $uploadDir . $siswa['foto'];
                if (file_exists($oldPhotoPath)) {
                    unlink($oldPhotoPath);
                }
            }

            // Move uploaded file
            if (!move_uploaded_file($file['tmp_name'], $filePath)) {
                throw new Exception('Gagal menyimpan file foto');
            }

            // Update database
            $updateData = ['foto' => $fileName];
            $result = $this->siswa->updateFoto($id, $updateData);

            if ($result) {
                // Log activity
                Security::logSecurityEvent('foto_siswa_uploaded', [
                    'siswa_id' => $id,
                    'filename' => $fileName,
                    'uploaded_by' => $_SESSION['user_id'] ?? 1
                ]);

                $_SESSION['success'] = 'Foto siswa berhasil diupload';
            } else {
                // Delete uploaded file if database update failed
                if (file_exists($filePath)) {
                    unlink($filePath);
                }
                throw new Exception('Gagal menyimpan data foto ke database');
            }

        } catch (Exception $e) {
            $_SESSION['error'] = $e->getMessage();
        }

        header('Location: /siswa-app/public/siswa/detail/' . $id);
        exit;
    }

    /**
     * Delete foto siswa
     */
    public function deleteFoto($id) {
        Security::requireAuth();

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $_SESSION['error'] = 'Method tidak diizinkan';
            header('Location: /siswa-app/public/siswa/detail/' . $id);
            exit;
        }

        // Verify CSRF token
        $csrfToken = $_POST['csrf_token'] ?? '';
        if (!Security::verifyCSRFToken($csrfToken)) {
            $_SESSION['error'] = 'Token keamanan tidak valid';
            header('Location: /siswa-app/public/siswa/detail/' . $id);
            exit;
        }

        // Get student data
        $siswa = $this->siswa->getById($id);
        if (!$siswa) {
            $_SESSION['error'] = 'Data siswa tidak ditemukan';
            header('Location: /siswa-app/public/siswa');
            exit;
        }

        try {
            if (!empty($siswa['foto'])) {
                // Delete physical file
                $uploadDir = __DIR__ . '/../../public/uploads/foto_siswa/';
                $photoPath = $uploadDir . $siswa['foto'];
                if (file_exists($photoPath)) {
                    unlink($photoPath);
                }

                // Update database
                $updateData = ['foto' => null];
                $result = $this->siswa->updateFoto($id, $updateData);

                if ($result) {
                    // Log activity
                    Security::logSecurityEvent('foto_siswa_deleted', [
                        'siswa_id' => $id,
                        'filename' => $siswa['foto'],
                        'deleted_by' => $_SESSION['user_id'] ?? 1
                    ]);

                    $_SESSION['success'] = 'Foto siswa berhasil dihapus';
                } else {
                    throw new Exception('Gagal menghapus data foto dari database');
                }
            } else {
                $_SESSION['error'] = 'Tidak ada foto yang dapat dihapus';
            }

        } catch (Exception $e) {
            $_SESSION['error'] = $e->getMessage();
        }

        header('Location: /siswa-app/public/siswa/detail/' . $id);
        exit;
    }

    private function view($path, $data = []) {
        extract($data);
        require_once __DIR__ . '/../views/layouts/header.php';
        require_once __DIR__ . '/../views/' . $path . '.php';
        require_once __DIR__ . '/../views/layouts/footer.php';
    }
}
?>