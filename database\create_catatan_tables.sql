-- =====================================================
-- TABEL CATATAN SISWA
-- =====================================================

-- Table: catatan_siswa (untuk semua jenis catatan)
CREATE TABLE IF NOT EXISTS catatan_siswa (
    id INT PRIMARY KEY AUTO_INCREMENT,
    siswa_id INT NOT NULL,
    jenis_catatan ENUM(
        'pamong_mp', 'pamong_mt', 'pamong_mm', 'pamong_mu',
        'wali_kpp', 'wali_x', 'wali_xi', 'wali_xii', 'wali_kpa',
        'bk_konseling', 'bk_pelanggaran', 'bk_prestasi', 'bk_lainnya'
    ) NOT NULL,
    judul_catatan VARCHAR(255) NOT NULL,
    isi_catatan TEXT NOT NULL,
    tanggal_catatan DATE NOT NULL,
    tingkat_prioritas ENUM('rendah', 'sedang', 'tinggi', 'urgent') DEFAULT 'sedang',
    status_catatan ENUM('draft', 'aktif', 'selesai', 'ditunda') DEFAULT 'aktif',
    tindak_lanjut TEXT,
    tanggal_tindak_lanjut DATE,
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by INT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (siswa_id) REFERENCES siswa(id_siswa) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id),
    FOREIGN KEY (updated_by) REFERENCES users(id),
    
    INDEX idx_siswa_jenis (siswa_id, jenis_catatan),
    INDEX idx_tanggal (tanggal_catatan),
    INDEX idx_status (status_catatan),
    INDEX idx_prioritas (tingkat_prioritas)
);

-- Table: kategori_catatan (untuk referensi kategori)
CREATE TABLE IF NOT EXISTS kategori_catatan (
    id INT PRIMARY KEY AUTO_INCREMENT,
    kode_kategori VARCHAR(20) NOT NULL UNIQUE,
    nama_kategori VARCHAR(100) NOT NULL,
    deskripsi TEXT,
    warna_badge VARCHAR(7) DEFAULT '#6c757d', -- hex color
    icon_class VARCHAR(50) DEFAULT 'bi-note-text',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert default kategori catatan
INSERT INTO kategori_catatan (kode_kategori, nama_kategori, deskripsi, warna_badge, icon_class) VALUES
-- Pamong
('pamong_mp', 'Pamong MP', 'Catatan Pamong Masa Persiapan', '#17a2b8', 'bi-person-badge'),
('pamong_mt', 'Pamong MT', 'Catatan Pamong Masa Transisi', '#28a745', 'bi-person-check'),
('pamong_mm', 'Pamong MM', 'Catatan Pamong Masa Mandiri', '#ffc107', 'bi-person-gear'),
('pamong_mu', 'Pamong MU', 'Catatan Pamong Masa Uji', '#fd7e14', 'bi-person-exclamation'),

-- Wali Kelas
('wali_kpp', 'Wali Kelas KPP', 'Catatan Wali Kelas Persiapan Profesi', '#6f42c1', 'bi-mortarboard'),
('wali_x', 'Wali Kelas X', 'Catatan Wali Kelas X', '#007bff', 'bi-book'),
('wali_xi', 'Wali Kelas XI', 'Catatan Wali Kelas XI', '#0d6efd', 'bi-journal'),
('wali_xii', 'Wali Kelas XII', 'Catatan Wali Kelas XII', '#6610f2', 'bi-graduation'),
('wali_kpa', 'Wali Kelas KPA', 'Catatan Wali Kelas Pasca', '#e83e8c', 'bi-award'),

-- BK (Bimbingan Konseling)
('bk_konseling', 'BK Konseling', 'Catatan Bimbingan Konseling', '#dc3545', 'bi-heart'),
('bk_pelanggaran', 'BK Pelanggaran', 'Catatan Pelanggaran Siswa', '#dc3545', 'bi-exclamation-triangle'),
('bk_prestasi', 'BK Prestasi', 'Catatan Prestasi Siswa', '#198754', 'bi-trophy'),
('bk_lainnya', 'BK Lainnya', 'Catatan BK Lainnya', '#6c757d', 'bi-chat-dots');

-- Table: template_catatan (untuk template catatan yang sering digunakan)
CREATE TABLE IF NOT EXISTS template_catatan (
    id INT PRIMARY KEY AUTO_INCREMENT,
    nama_template VARCHAR(100) NOT NULL,
    jenis_catatan VARCHAR(20) NOT NULL,
    template_judul VARCHAR(255) NOT NULL,
    template_isi TEXT NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (created_by) REFERENCES users(id),
    INDEX idx_jenis (jenis_catatan)
);

-- Insert template catatan default
INSERT INTO template_catatan (nama_template, jenis_catatan, template_judul, template_isi, created_by) VALUES
-- Template Pamong
('Evaluasi Bulanan MP', 'pamong_mp', 'Evaluasi Bulanan - [Bulan] [Tahun]', 'Perkembangan siswa selama bulan ini:\n\n1. Aspek Akademik:\n   - \n\n2. Aspek Kepribadian:\n   - \n\n3. Aspek Sosial:\n   - \n\nRekomendasi tindak lanjut:\n- ', 1),
('Konsultasi Wali', 'wali_x', 'Konsultasi dengan Orang Tua', 'Hasil konsultasi dengan orang tua:\n\nTopik yang dibahas:\n- \n\nKesepakatan:\n- \n\nTindak lanjut:\n- ', 1),
('Sesi Konseling', 'bk_konseling', 'Sesi Konseling Individual', 'Sesi konseling tanggal [tanggal]\n\nMasalah yang dibahas:\n- \n\nPendekatan yang digunakan:\n- \n\nHasil dan progress:\n- \n\nRencana sesi berikutnya:\n- ', 1);
