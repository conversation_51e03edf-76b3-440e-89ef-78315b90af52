<?php
/**
 * Script untuk mengganti tema warna pada halaman detail siswa
 * Jalankan script ini untuk mengganti warna background header
 */

$detailFile = 'app/views/siswa/detail.php';

// <PERSON><PERSON>han warna yang tersedia
$colorOptions = [
    '1' => [
        'name' => 'Soft Gray (Current)',
        'description' => 'Professional dan netral - cocok untuk semua situasi',
        'gradient' => 'background: linear-gradient(135deg, #6c757d 0%, #495057 100%);',
        'border' => 'background: linear-gradient(to bottom, #6c757d, #495057);'
    ],
    '2' => [
        'name' => 'Soft Blue',
        'description' => 'Tenang dan trustworthy - memberikan kesan dapat dipercaya',
        'gradient' => 'background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);',
        'border' => 'background: linear-gradient(to bottom, #74b9ff, #0984e3);'
    ],
    '3' => [
        'name' => 'Soft Teal',
        'description' => 'Modern dan fresh - memberikan kesan segar dan modern',
        'gradient' => 'background: linear-gradient(135deg, #00b894 0%, #00a085 100%);',
        'border' => 'background: linear-gradient(to bottom, #00b894, #00a085);'
    ],
    '4' => [
        'name' => 'Soft Navy',
        'description' => 'Elegant dan professional - sangat formal dan elegan',
        'gradient' => 'background: linear-gradient(135deg, #2d3436 0%, #636e72 100%);',
        'border' => 'background: linear-gradient(to bottom, #2d3436, #636e72);'
    ],
    '5' => [
        'name' => 'Soft Green',
        'description' => 'Natural dan calming - memberikan kesan tenang dan alami',
        'gradient' => 'background: linear-gradient(135deg, #55a3ff 0%, #003d82 100%);',
        'border' => 'background: linear-gradient(to bottom, #55a3ff, #003d82);'
    ],
    '6' => [
        'name' => 'Warm Brown',
        'description' => 'Warm dan earthy - memberikan kesan hangat dan bersahabat',
        'gradient' => 'background: linear-gradient(135deg, #8d6e63 0%, #5d4037 100%);',
        'border' => 'background: linear-gradient(to bottom, #8d6e63, #5d4037);'
    ]
];

echo "🎨 THEME COLOR CHANGER - Detail Siswa\n";
echo "=====================================\n\n";

echo "Pilihan warna yang tersedia:\n\n";
foreach ($colorOptions as $key => $option) {
    echo "[$key] {$option['name']}\n";
    echo "    {$option['description']}\n\n";
}

echo "Masukkan pilihan Anda (1-6): ";
$choice = trim(fgets(STDIN));

if (!isset($colorOptions[$choice])) {
    echo "❌ Pilihan tidak valid!\n";
    exit;
}

$selectedOption = $colorOptions[$choice];

echo "\n🎨 Mengganti tema ke: {$selectedOption['name']}\n";
echo "📝 {$selectedOption['description']}\n\n";

// Read the current file
if (!file_exists($detailFile)) {
    echo "❌ File detail.php tidak ditemukan!\n";
    exit;
}

$content = file_get_contents($detailFile);

// Replace the gradient background
$oldPattern = '/\.bg-gradient-soft \{[^}]+\}/s';
$newGradient = ".bg-gradient-soft {\n    {$selectedOption['gradient']}\n}";

$content = preg_replace($oldPattern, $newGradient, $content);

// Replace the info-group border gradient
$oldBorderPattern = '/\.info-group::before \{[^}]+\}/s';
$newBorder = ".info-group::before {\n    content: '';\n    position: absolute;\n    left: 0;\n    top: 0;\n    bottom: 0;\n    width: 3px;\n    {$selectedOption['border']}\n    border-radius: 2px;\n}";

$content = preg_replace($oldBorderPattern, $newBorder, $content);

// Write back to file
if (file_put_contents($detailFile, $content)) {
    echo "✅ Tema berhasil diubah!\n";
    echo "🌐 Refresh browser untuk melihat perubahan.\n\n";
    
    echo "📋 INSTRUKSI:\n";
    echo "1. Buka browser dan refresh halaman detail siswa\n";
    echo "2. Jika tidak puas dengan warna, jalankan script ini lagi\n";
    echo "3. Untuk kembali ke warna default, pilih option 1 (Soft Gray)\n\n";
    
    echo "🎨 Warna yang dipilih: {$selectedOption['name']}\n";
    echo "💡 {$selectedOption['description']}\n";
} else {
    echo "❌ Gagal mengubah file!\n";
    echo "Pastikan file memiliki permission untuk ditulis.\n";
}

echo "\n🚀 Selesai!\n";
?>
