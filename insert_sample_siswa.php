<?php
/**
 * Script untuk menambahkan data sample siswa
 */

require_once 'app/config/db.php';

try {
    $pdo = getDBConnection();
    
    // Get available kelas
    $kelasResult = $pdo->query("SELECT id, nama_kelas, tingkat FROM kelas WHERE is_active = 1 ORDER BY tingkat, nama_kelas");
    $kelasList = $kelasResult->fetchAll();
    
    if (empty($kelasList)) {
        echo "Tidak ada kelas yang tersedia. Jalankan script insert kelas terlebih dahulu.\n";
        exit;
    }
    
    echo "Kelas yang tersedia:\n";
    foreach ($kelasList as $kelas) {
        echo "- ID: {$kelas['id']}, Nama: {$kelas['nama_kelas']} (Tingkat {$kelas['tingkat']})\n";
    }
    echo "\n";
    
    // Sample data siswa
    $siswaData = [
        [
            'nis' => '2024001',
            'nisn' => '1234567890',
            'nama_lengkap' => '<PERSON>',
            'jenis_kelamin' => 'L',
            'tempat_lahir' => 'Jakarta',
            'tanggal_lahir' => '2008-03-15',
            'alamat' => 'Jl. Merdeka No. 123, Jakarta Pusat',
            'no_telepon' => '081234567890',
            'email' => '<EMAIL>',
            'nama_ayah' => 'Budi Pratama',
            'nama_ibu' => 'Siti Nurhaliza',
            'pekerjaan_ayah' => 'Pegawai Swasta',
            'pekerjaan_ibu' => 'Ibu Rumah Tangga',
            'tahun_masuk' => 2024,
            'status_siswa' => 'aktif'
        ],
        [
            'nis' => '2024002',
            'nisn' => '1234567891',
            'nama_lengkap' => 'Sari Dewi Lestari',
            'jenis_kelamin' => 'P',
            'tempat_lahir' => 'Bandung',
            'tanggal_lahir' => '2008-07-22',
            'alamat' => 'Jl. Sudirman No. 456, Bandung',
            'no_telepon' => '081234567891',
            'email' => '<EMAIL>',
            'nama_ayah' => 'Agus Lestari',
            'nama_ibu' => 'Rina Sari',
            'pekerjaan_ayah' => 'Guru',
            'pekerjaan_ibu' => 'Perawat',
            'tahun_masuk' => 2024,
            'status_siswa' => 'aktif'
        ],
        [
            'nis' => '2024003',
            'nisn' => '1234567892',
            'nama_lengkap' => 'Muhammad Fajar Sidiq',
            'jenis_kelamin' => 'L',
            'tempat_lahir' => 'Surabaya',
            'tanggal_lahir' => '2008-11-08',
            'alamat' => 'Jl. Pahlawan No. 789, Surabaya',
            'no_telepon' => '081234567892',
            'email' => '<EMAIL>',
            'nama_ayah' => 'Hendra Sidiq',
            'nama_ibu' => 'Fatimah',
            'pekerjaan_ayah' => 'Wiraswasta',
            'pekerjaan_ibu' => 'Guru',
            'tahun_masuk' => 2024,
            'status_siswa' => 'aktif'
        ],
        [
            'nis' => '2024004',
            'nisn' => '1234567893',
            'nama_lengkap' => 'Putri Ayu Ningrum',
            'jenis_kelamin' => 'P',
            'tempat_lahir' => 'Yogyakarta',
            'tanggal_lahir' => '2008-05-12',
            'alamat' => 'Jl. Malioboro No. 321, Yogyakarta',
            'no_telepon' => '081234567893',
            'email' => '<EMAIL>',
            'nama_ayah' => 'Bambang Ningrum',
            'nama_ibu' => 'Sri Wahyuni',
            'pekerjaan_ayah' => 'PNS',
            'pekerjaan_ibu' => 'Dokter',
            'tahun_masuk' => 2024,
            'status_siswa' => 'aktif'
        ],
        [
            'nis' => '2024005',
            'nisn' => '1234567894',
            'nama_lengkap' => 'Dimas Arya Wijaya',
            'jenis_kelamin' => 'L',
            'tempat_lahir' => 'Medan',
            'tanggal_lahir' => '2008-09-30',
            'alamat' => 'Jl. Gatot Subroto No. 654, Medan',
            'no_telepon' => '081234567894',
            'email' => '<EMAIL>',
            'nama_ayah' => 'Arief Wijaya',
            'nama_ibu' => 'Maya Sari',
            'pekerjaan_ayah' => 'Insinyur',
            'pekerjaan_ibu' => 'Akuntan',
            'tahun_masuk' => 2024,
            'status_siswa' => 'aktif'
        ]
    ];
    
    // Check if siswa data already exists
    $existingData = $pdo->query("SELECT COUNT(*) as count FROM siswa")->fetch();
    if ($existingData['count'] > 0) {
        echo "Data siswa sudah ada (" . $existingData['count'] . " records). Menambahkan data baru...\n";
    }
    
    $insertedCount = 0;
    $skippedCount = 0;
    
    foreach ($siswaData as $index => $siswa) {
        // Check if student already exists
        $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM siswa WHERE nis = ?");
        $stmt->execute([$siswa['nis']]);
        $exists = $stmt->fetch();
        
        if ($exists['count'] == 0) {
            // Assign to random kelas
            $randomKelas = $kelasList[array_rand($kelasList)];
            $siswa['kelas_id'] = $randomKelas['id'];
            
            // Insert new student
            $insertStmt = $pdo->prepare("
                INSERT INTO siswa (
                    nis, nisn, nama_lengkap, jenis_kelamin, tempat_lahir, tanggal_lahir,
                    alamat, no_telepon, email, nama_ayah, nama_ibu, pekerjaan_ayah, 
                    pekerjaan_ibu, kelas_id, tahun_masuk, status_siswa, created_by, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1, NOW())
            ");
            
            $result = $insertStmt->execute([
                $siswa['nis'],
                $siswa['nisn'],
                $siswa['nama_lengkap'],
                $siswa['jenis_kelamin'],
                $siswa['tempat_lahir'],
                $siswa['tanggal_lahir'],
                $siswa['alamat'],
                $siswa['no_telepon'],
                $siswa['email'],
                $siswa['nama_ayah'],
                $siswa['nama_ibu'],
                $siswa['pekerjaan_ayah'],
                $siswa['pekerjaan_ibu'],
                $siswa['kelas_id'],
                $siswa['tahun_masuk'],
                $siswa['status_siswa']
            ]);
            
            if ($result) {
                echo "✓ Berhasil menambahkan siswa: " . $siswa['nama_lengkap'] . " (NIS: " . $siswa['nis'] . ") ke kelas " . $randomKelas['nama_kelas'] . "\n";
                $insertedCount++;
            } else {
                echo "✗ Gagal menambahkan siswa: " . $siswa['nama_lengkap'] . "\n";
            }
        } else {
            echo "- Siswa dengan NIS " . $siswa['nis'] . " sudah ada, dilewati.\n";
            $skippedCount++;
        }
    }
    
    echo "\n=== SUMMARY ===\n";
    echo "Total siswa baru yang ditambahkan: $insertedCount\n";
    echo "Total siswa yang dilewati: $skippedCount\n";
    
    // Show current data
    $allSiswa = $pdo->query("
        SELECT s.nama_lengkap, s.nis, s.jenis_kelamin, k.nama_kelas, s.status_siswa
        FROM siswa s
        LEFT JOIN kelas k ON s.kelas_id = k.id
        ORDER BY s.nama_lengkap
    ")->fetchAll();
    
    echo "Total siswa saat ini: " . count($allSiswa) . "\n\n";
    
    echo "Daftar siswa:\n";
    foreach ($allSiswa as $s) {
        $gender = $s['jenis_kelamin'] == 'L' ? 'Laki-laki' : 'Perempuan';
        echo "- {$s['nama_lengkap']} (NIS: {$s['nis']}) - $gender - Kelas: {$s['nama_kelas']} - Status: {$s['status_siswa']}\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Pastikan database sudah dikonfigurasi dengan benar.\n";
}
?>
