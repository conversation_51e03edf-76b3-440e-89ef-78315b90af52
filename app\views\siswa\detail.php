<?php
// Check if siswa data exists
if (!isset($siswa) || empty($siswa)) {
    echo '<div class="alert alert-danger">Data siswa tidak ditemukan.</div>';
    return;
}

// Load berkas model safely
try {
    require_once __DIR__ . '/../../models/Berkas.php';
    $berkas_model = new Berkas();
    $berkas = $berkas_model->getBySiswaId($siswa['id_siswa'] ?? $siswa['id'] ?? 0);
} catch (Exception $e) {
    $berkas = [];
}
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0 text-gray-800">
                        <i class="bi bi-person-circle text-primary"></i>
                        Detail Siswa
                    </h1>
                    <p class="text-muted mb-0">Informasi lengkap siswa</p>
                </div>
                <div>
                    <a href="/siswa-app/public/siswa" class="btn btn-secondary">
                        <i class="bi bi-arrow-left"></i>
                        Kembali
                    </a>
                    <a href="/siswa-app/public/siswa/edit/<?= $siswa['id_siswa'] ?? $siswa['id'] ?>" class="btn btn-warning">
                        <i class="bi bi-pencil"></i>
                        Edit
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Student Profile Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-lg border-0">
                <div class="card-body p-0">
                    <div class="row g-0">
                        <!-- Photo Section -->
                        <div class="col-md-4 bg-white d-flex align-items-center justify-content-center" style="min-height: 300px; border-right: 1px solid #e9ecef;">
                            <div class="text-center p-4">
                                <?php if (!empty($siswa['foto'])): ?>
                                    <div class="position-relative d-inline-block mb-3">
                                        <img src="/siswa-app/public/uploads/foto_siswa/<?= htmlspecialchars($siswa['foto']) ?>"
                                             alt="Foto <?= htmlspecialchars($siswa['nama_lengkap'] ?? $siswa['nama'] ?? 'Siswa') ?>"
                                             class="img-fluid rounded-circle shadow-lg border border-white border-4"
                                             style="width: 180px; height: 180px; object-fit: cover;">
                                        <div class="position-absolute top-0 end-0">
                                            <button type="button" class="btn btn-danger btn-sm rounded-circle shadow"
                                                    onclick="deleteFoto(<?= $siswa['id_siswa'] ?? $siswa['id'] ?>)"
                                                    title="Hapus Foto">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                <?php else: ?>
                                    <div class="bg-light rounded-circle mx-auto d-flex align-items-center justify-content-center mb-3"
                                         style="width: 180px; height: 180px;">
                                        <i class="bi bi-person" style="font-size: 4rem; color: #dee2e6;"></i>
                                    </div>
                                <?php endif; ?>

                                <button type="button" class="btn btn-primary btn-sm shadow" data-bs-toggle="modal" data-bs-target="#uploadFotoModal">
                                    <i class="bi bi-camera-fill"></i>
                                    <?= !empty($siswa['foto']) ? 'Ganti Foto' : 'Upload Foto' ?>
                                </button>
                            </div>
                        </div>

                        <!-- Basic Info Section -->
                        <div class="col-md-8">
                            <div class="p-4">
                                <div class="d-flex justify-content-between align-items-start mb-3">
                                    <div>
                                        <h2 class="fw-bold text-dark mb-1"><?= htmlspecialchars($siswa['nama_lengkap'] ?? $siswa['nama'] ?? 'N/A') ?></h2>
                                        <p class="text-muted mb-2">
                                            <i class="bi bi-card-text me-1"></i>
                                            NIS: <strong><?= htmlspecialchars($siswa['nis'] ?? 'N/A') ?></strong>
                                            <?php if (!empty($siswa['nisn'])): ?>
                                                | NISN: <strong><?= htmlspecialchars($siswa['nisn']) ?></strong>
                                            <?php endif; ?>
                                        </p>
                                    </div>
                                    <div class="text-end">
                                        <?php
                                        $status = $siswa['status_siswa'] ?? 'aktif';
                                        $statusClass = [
                                            'aktif' => 'bg-success',
                                            'lulus' => 'bg-primary',
                                            'mutasi' => 'bg-warning',
                                            'dropout' => 'bg-danger'
                                        ];
                                        ?>
                                        <span class="badge <?= $statusClass[$status] ?? 'bg-secondary' ?> fs-6 px-3 py-2">
                                            <i class="bi bi-person-check me-1"></i>
                                            <?= ucfirst($status) ?>
                                        </span>
                                    </div>
                                </div>

                                <div class="row g-3">
                                    <div class="col-sm-6">
                                        <div class="d-flex align-items-center">
                                            <div class="bg-primary bg-opacity-10 rounded-circle p-2 me-3">
                                                <?php
                                                $gender = $siswa['jenis_kelamin'] ?? 'L';
                                                $genderIcon = $gender === 'L' ? 'bi-gender-male text-primary' : 'bi-gender-female text-danger';
                                                $genderText = $gender === 'L' ? 'Laki-laki' : 'Perempuan';
                                                ?>
                                                <i class="bi <?= $genderIcon ?>"></i>
                                            </div>
                                            <div>
                                                <small class="text-muted d-block">Jenis Kelamin</small>
                                                <strong><?= $genderText ?></strong>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-sm-6">
                                        <div class="d-flex align-items-center">
                                            <div class="bg-info bg-opacity-10 rounded-circle p-2 me-3">
                                                <i class="bi bi-building text-info"></i>
                                            </div>
                                            <div>
                                                <small class="text-muted d-block">Kelas</small>
                                                <?php if (!empty($siswa['nama_kelas'])): ?>
                                                    <strong><?= htmlspecialchars($siswa['nama_kelas']) ?></strong>
                                                <?php else: ?>
                                                    <span class="text-muted">Belum Ada Kelas</span>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-sm-6">
                                        <div class="d-flex align-items-center">
                                            <div class="bg-warning bg-opacity-10 rounded-circle p-2 me-3">
                                                <i class="bi bi-calendar-event text-warning"></i>
                                            </div>
                                            <div>
                                                <small class="text-muted d-block">Tahun Masuk</small>
                                                <strong><?= htmlspecialchars($siswa['tahun_masuk'] ?? 'N/A') ?></strong>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-sm-6">
                                        <div class="d-flex align-items-center">
                                            <div class="bg-success bg-opacity-10 rounded-circle p-2 me-3">
                                                <i class="bi bi-envelope text-success"></i>
                                            </div>
                                            <div>
                                                <small class="text-muted d-block">Email</small>
                                                <?php if (!empty($siswa['email'])): ?>
                                                    <strong><?= htmlspecialchars($siswa['email']) ?></strong>
                                                <?php else: ?>
                                                    <span class="text-muted">Tidak ada</span>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Personal Information -->
        <div class="col-lg-8 mb-4">
            <div class="card shadow border-0">
                <div class="card-header bg-white border-bottom-0 py-3">
                    <h5 class="mb-0 text-dark fw-bold">
                        <i class="bi bi-person-lines-fill text-primary me-2"></i>
                        Informasi Personal
                    </h5>
                </div>
                <div class="card-body p-4">
                    <div class="row g-4">
                        <!-- Birth Information -->
                        <div class="col-md-6">
                            <div class="info-group">
                                <h6 class="text-muted mb-3 fw-bold">
                                    <i class="bi bi-calendar-heart text-danger me-2"></i>
                                    Data Kelahiran
                                </h6>
                                <div class="info-item mb-3">
                                    <div class="d-flex align-items-center">
                                        <div class="bg-danger bg-opacity-10 rounded-circle p-2 me-3">
                                            <i class="bi bi-geo-alt text-danger"></i>
                                        </div>
                                        <div>
                                            <small class="text-muted d-block">Tempat Lahir</small>
                                            <strong><?= htmlspecialchars($siswa['tempat_lahir'] ?? 'Tidak diketahui') ?></strong>
                                        </div>
                                    </div>
                                </div>
                                <div class="info-item mb-3">
                                    <div class="d-flex align-items-center">
                                        <div class="bg-danger bg-opacity-10 rounded-circle p-2 me-3">
                                            <i class="bi bi-calendar-date text-danger"></i>
                                        </div>
                                        <div>
                                            <small class="text-muted d-block">Tanggal Lahir</small>
                                            <strong>
                                                <?php
                                                if (!empty($siswa['tanggal_lahir'])) {
                                                    $birthDate = new DateTime($siswa['tanggal_lahir']);
                                                    $today = new DateTime();
                                                    $age = $today->diff($birthDate)->y;
                                                    echo $birthDate->format('d F Y') . " <small class='text-muted'>($age tahun)</small>";
                                                } else {
                                                    echo 'Tidak diketahui';
                                                }
                                                ?>
                                            </strong>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Contact Information -->
                        <div class="col-md-6">
                            <div class="info-group">
                                <h6 class="text-muted mb-3 fw-bold">
                                    <i class="bi bi-telephone text-success me-2"></i>
                                    Kontak
                                </h6>
                                <div class="info-item mb-3">
                                    <div class="d-flex align-items-center">
                                        <div class="bg-success bg-opacity-10 rounded-circle p-2 me-3">
                                            <i class="bi bi-phone text-success"></i>
                                        </div>
                                        <div>
                                            <small class="text-muted d-block">No. Telepon</small>
                                            <?php if (!empty($siswa['no_telepon'])): ?>
                                                <strong><?= htmlspecialchars($siswa['no_telepon']) ?></strong>
                                                <a href="tel:<?= htmlspecialchars($siswa['no_telepon']) ?>" class="btn btn-sm btn-outline-success ms-2">
                                                    <i class="bi bi-telephone"></i>
                                                </a>
                                            <?php else: ?>
                                                <span class="text-muted">Tidak ada</span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                                <div class="info-item mb-3">
                                    <div class="d-flex align-items-center">
                                        <div class="bg-success bg-opacity-10 rounded-circle p-2 me-3">
                                            <i class="bi bi-envelope text-success"></i>
                                        </div>
                                        <div>
                                            <small class="text-muted d-block">Email</small>
                                            <?php if (!empty($siswa['email'])): ?>
                                                <strong><?= htmlspecialchars($siswa['email']) ?></strong>
                                                <a href="mailto:<?= htmlspecialchars($siswa['email']) ?>" class="btn btn-sm btn-outline-success ms-2">
                                                    <i class="bi bi-envelope"></i>
                                                </a>
                                            <?php else: ?>
                                                <span class="text-muted">Tidak ada</span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Address Section -->
                    <?php if (!empty($siswa['alamat'])): ?>
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="info-group">
                                <h6 class="text-muted mb-3 fw-bold">
                                    <i class="bi bi-house text-info me-2"></i>
                                    Alamat Lengkap
                                </h6>
                                <div class="bg-light rounded p-3">
                                    <div class="d-flex">
                                        <div class="bg-info bg-opacity-10 rounded-circle p-2 me-3 flex-shrink-0">
                                            <i class="bi bi-geo-alt-fill text-info"></i>
                                        </div>
                                        <div>
                                            <p class="mb-0"><?= nl2br(htmlspecialchars($siswa['alamat'])) ?></p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>

                    <!-- Academic Information -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="info-group">
                                <h6 class="text-muted mb-3 fw-bold">
                                    <i class="bi bi-mortarboard text-warning me-2"></i>
                                    Informasi Akademik
                                </h6>
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <div class="bg-light rounded p-3">
                                            <div class="d-flex align-items-center">
                                                <div class="bg-warning bg-opacity-10 rounded-circle p-2 me-3">
                                                    <i class="bi bi-calendar-plus text-warning"></i>
                                                </div>
                                                <div>
                                                    <small class="text-muted d-block">Tahun Masuk</small>
                                                    <strong class="fs-5"><?= htmlspecialchars($siswa['tahun_masuk'] ?? 'N/A') ?></strong>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="bg-light rounded p-3">
                                            <div class="d-flex align-items-center">
                                                <div class="bg-warning bg-opacity-10 rounded-circle p-2 me-3">
                                                    <i class="bi bi-clock-history text-warning"></i>
                                                </div>
                                                <div>
                                                    <small class="text-muted d-block">Terdaftar Sejak</small>
                                                    <strong>
                                                        <?php
                                                        if (!empty($siswa['created_at'])) {
                                                            echo date('d F Y', strtotime($siswa['created_at']));
                                                        } else {
                                                            echo 'N/A';
                                                        }
                                                        ?>
                                                    </strong>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Parent Information & Quick Actions -->
        <div class="col-lg-4">
            <!-- Quick Actions -->
            <div class="card shadow border-0 mb-4">
                <div class="card-header bg-primary text-white border-0 py-3">
                    <h6 class="mb-0 fw-bold">
                        <i class="bi bi-lightning-charge me-2"></i>
                        Aksi Cepat
                    </h6>
                </div>
                <div class="card-body p-3">
                    <div class="d-grid gap-2">
                        <a href="/siswa-app/public/siswa/edit/<?= $siswa['id_siswa'] ?? $siswa['id'] ?>" class="btn btn-warning">
                            <i class="bi bi-pencil-square me-2"></i>
                            Edit Data Siswa
                        </a>
                        <a href="/siswa-app/public/upload/berkas/<?= $siswa['id_siswa'] ?? $siswa['id'] ?>" class="btn btn-success">
                            <i class="bi bi-cloud-upload me-2"></i>
                            Upload Berkas
                        </a>
                        <a href="/siswa-app/public/catatan/add/<?= $siswa['id_siswa'] ?? $siswa['id'] ?>" class="btn btn-info">
                            <i class="bi bi-journal-plus me-2"></i>
                            Tambah Catatan
                        </a>
                        <a href="/siswa-app/public/siswa" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-left me-2"></i>
                            Kembali ke Daftar
                        </a>
                    </div>
                </div>
            </div>

            <!-- Parent Information -->
            <div class="card shadow border-0 mb-4">
                <div class="card-header bg-white border-bottom-0 py-3">
                    <h6 class="mb-0 text-dark fw-bold">
                        <i class="bi bi-people-fill text-purple me-2"></i>
                        Data Orang Tua
                    </h6>
                </div>
                <div class="card-body p-4">
                    <!-- Father Information -->
                    <div class="parent-info mb-4">
                        <div class="d-flex align-items-center mb-3">
                            <div class="bg-primary bg-opacity-10 rounded-circle p-2 me-3">
                                <i class="bi bi-person-fill text-primary"></i>
                            </div>
                            <h6 class="mb-0 fw-bold text-primary">Ayah</h6>
                        </div>
                        <div class="ps-4">
                            <div class="mb-2">
                                <small class="text-muted d-block">Nama Lengkap</small>
                                <strong><?= htmlspecialchars($siswa['nama_ayah'] ?? 'Tidak diketahui') ?></strong>
                            </div>
                            <div class="mb-2">
                                <small class="text-muted d-block">Pekerjaan</small>
                                <span class="badge bg-primary bg-opacity-10 text-primary">
                                    <?= htmlspecialchars($siswa['pekerjaan_ayah'] ?? 'Tidak diketahui') ?>
                                </span>
                            </div>
                        </div>
                    </div>

                    <!-- Mother Information -->
                    <div class="parent-info">
                        <div class="d-flex align-items-center mb-3">
                            <div class="bg-danger bg-opacity-10 rounded-circle p-2 me-3">
                                <i class="bi bi-person-fill text-danger"></i>
                            </div>
                            <h6 class="mb-0 fw-bold text-danger">Ibu</h6>
                        </div>
                        <div class="ps-4">
                            <div class="mb-2">
                                <small class="text-muted d-block">Nama Lengkap</small>
                                <strong><?= htmlspecialchars($siswa['nama_ibu'] ?? 'Tidak diketahui') ?></strong>
                            </div>
                            <div class="mb-2">
                                <small class="text-muted d-block">Pekerjaan</small>
                                <span class="badge bg-danger bg-opacity-10 text-danger">
                                    <?= htmlspecialchars($siswa['pekerjaan_ibu'] ?? 'Tidak diketahui') ?>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Files Summary -->
            <div class="card shadow border-0">
                <div class="card-header bg-white border-bottom-0 py-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <h6 class="mb-0 text-dark fw-bold">
                            <i class="bi bi-folder2-open text-warning me-2"></i>
                            Berkas Siswa
                        </h6>
                        <a href="/siswa-app/public/berkas/index/<?= $siswa['id_siswa'] ?? $siswa['id'] ?>" class="btn btn-outline-primary btn-sm">
                            <i class="bi bi-eye me-1"></i>
                            Lihat Semua
                        </a>
                    </div>
                </div>
                <div class="card-body p-4">
                    <?php if (!empty($berkas)): ?>
                        <?php
                        // Group berkas by category
                        $berkasModel = new Berkas();
                        $fileCategories = $berkasModel->getFileCategories();
                        $allowedTypes = $berkasModel->getAllowedTypes();

                        // Group files by category
                        $groupedBerkas = [];
                        foreach ($berkas as $b) {
                            $category = null;
                            foreach ($fileCategories as $catName => $types) {
                                if (array_key_exists($b['jenis_berkas'], $types)) {
                                    $category = $catName;
                                    break;
                                }
                            }
                            if (!$category) $category = 'Lainnya';
                            $groupedBerkas[$category][] = $b;
                        }
                        ?>

                        <!-- File Statistics -->
                        <div class="row g-3 mb-4">
                            <div class="col-6">
                                <div class="bg-primary bg-opacity-10 rounded p-3 text-center">
                                    <i class="bi bi-files text-primary fs-4"></i>
                                    <div class="mt-2">
                                        <strong class="d-block"><?= count($berkas) ?></strong>
                                        <small class="text-muted">Total Berkas</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="bg-success bg-opacity-10 rounded p-3 text-center">
                                    <i class="bi bi-folder text-success fs-4"></i>
                                    <div class="mt-2">
                                        <strong class="d-block"><?= count($groupedBerkas) ?></strong>
                                        <small class="text-muted">Kategori</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Recent Files -->
                        <h6 class="text-muted mb-3 fw-bold">
                            <i class="bi bi-clock-history me-2"></i>
                            Berkas Terbaru
                        </h6>

                        <?php
                        // Show only latest 3 files
                        $recentFiles = array_slice($berkas, 0, 3);
                        ?>

                        <?php foreach ($recentFiles as $b): ?>
                            <div class="d-flex align-items-center p-3 bg-light rounded mb-2">
                                <div class="bg-white rounded p-2 me-3">
                                    <?php
                                    $fileExt = strtolower(pathinfo($b['nama_file_asli'] ?? '', PATHINFO_EXTENSION));
                                    switch($fileExt) {
                                        case 'pdf':
                                            $iconClass = 'bi-file-earmark-pdf text-danger';
                                            break;
                                        case 'doc':
                                        case 'docx':
                                            $iconClass = 'bi-file-earmark-word text-primary';
                                            break;
                                        case 'jpg':
                                        case 'jpeg':
                                        case 'png':
                                        case 'gif':
                                            $iconClass = 'bi-file-earmark-image text-success';
                                            break;
                                        default:
                                            $iconClass = 'bi-file-earmark text-secondary';
                                    }
                                    ?>
                                    <i class="bi <?= $iconClass ?> fs-5"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-1" style="font-size: 14px;">
                                        <?= htmlspecialchars($b['nama_berkas'] ?? 'File') ?>
                                    </h6>
                                    <small class="text-muted">
                                        <?= $allowedTypes[$b['jenis_berkas']] ?? ucfirst(str_replace('_', ' ', $b['jenis_berkas'])) ?>
                                        • <?= number_format(($b['ukuran_file'] ?? 0) / 1024, 1) ?> KB
                                    </small>
                                </div>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="dropdown">
                                        <i class="bi bi-three-dots-vertical"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li>
                                            <a class="dropdown-item" href="/siswa-app/public/upload/download/<?= $b['id'] ?>">
                                                <i class="bi bi-download me-2"></i>Download
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        <?php endforeach; ?>

                        <?php if (count($berkas) > 3): ?>
                            <div class="text-center mt-3">
                                <a href="/siswa-app/public/berkas/index/<?= $siswa['id_siswa'] ?? $siswa['id'] ?>" class="btn btn-outline-primary btn-sm">
                                    <i class="bi bi-plus-circle me-1"></i>
                                    Lihat <?= count($berkas) - 3 ?> berkas lainnya
                                </a>
                            </div>
                        <?php endif; ?>

                    <?php else: ?>
                        <div class="text-center py-4">
                            <div class="bg-light rounded-circle mx-auto d-flex align-items-center justify-content-center mb-3"
                                 style="width: 80px; height: 80px;">
                                <i class="bi bi-file-earmark-plus" style="font-size: 2rem; color: #dee2e6;"></i>
                            </div>
                            <h6 class="text-muted mb-2">Belum ada berkas</h6>
                            <p class="text-muted small mb-3">Upload berkas pertama untuk siswa ini</p>
                            <a href="/siswa-app/public/upload/berkas/<?= $siswa['id_siswa'] ?? $siswa['id'] ?>" class="btn btn-primary btn-sm">
                                <i class="bi bi-cloud-upload me-1"></i>
                                Upload Berkas
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Catatan Siswa Section -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="bi bi-journal-text"></i>
                            Catatan Siswa
                        </h6>
                        <div class="btn-group">
                            <a href="/siswa-app/public/catatan/add/<?= $siswa['id_siswa'] ?? $siswa['id'] ?>" class="btn btn-primary btn-sm">
                                <i class="bi bi-plus-circle"></i>
                                Tambah Catatan
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Tabs for different categories -->
                    <ul class="nav nav-tabs" id="catatanTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="pamong-tab" data-bs-toggle="tab" data-bs-target="#pamong" type="button" role="tab">
                                <i class="bi bi-person-badge"></i>
                                Catatan Pamong
                                <?php if (!empty($catatan_grouped['pamong'])): ?>
                                    <span class="badge bg-info ms-1"><?= count($catatan_grouped['pamong']) ?></span>
                                <?php endif; ?>
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="wali-tab" data-bs-toggle="tab" data-bs-target="#wali" type="button" role="tab">
                                <i class="bi bi-mortarboard"></i>
                                Catatan Wali Kelas
                                <?php if (!empty($catatan_grouped['wali_kelas'])): ?>
                                    <span class="badge bg-success ms-1"><?= count($catatan_grouped['wali_kelas']) ?></span>
                                <?php endif; ?>
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="bk-tab" data-bs-toggle="tab" data-bs-target="#bk" type="button" role="tab">
                                <i class="bi bi-heart"></i>
                                Catatan BK
                                <?php if (!empty($catatan_grouped['bk'])): ?>
                                    <span class="badge bg-warning ms-1"><?= count($catatan_grouped['bk']) ?></span>
                                <?php endif; ?>
                            </button>
                        </li>
                    </ul>

                    <div class="tab-content mt-3" id="catatanTabContent">
                        <!-- Pamong Tab -->
                        <div class="tab-pane fade show active" id="pamong" role="tabpanel">
                            <?php if (!empty($catatan_grouped['pamong'])): ?>
                                <?php foreach ($catatan_grouped['pamong'] as $catatan): ?>
                                    <?php include __DIR__ . '/components/catatan_card.php'; ?>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <div class="text-center py-4">
                                    <i class="bi bi-journal" style="font-size: 3rem; color: #dee2e6;"></i>
                                    <h6 class="text-muted mt-3">Belum ada catatan pamong</h6>
                                    <p class="text-muted small">Catatan pamong akan muncul di sini</p>
                                </div>
                            <?php endif; ?>
                        </div>

                        <!-- Wali Kelas Tab -->
                        <div class="tab-pane fade" id="wali" role="tabpanel">
                            <?php if (!empty($catatan_grouped['wali_kelas'])): ?>
                                <?php foreach ($catatan_grouped['wali_kelas'] as $catatan): ?>
                                    <?php include __DIR__ . '/components/catatan_card.php'; ?>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <div class="text-center py-4">
                                    <i class="bi bi-mortarboard" style="font-size: 3rem; color: #dee2e6;"></i>
                                    <h6 class="text-muted mt-3">Belum ada catatan wali kelas</h6>
                                    <p class="text-muted small">Catatan wali kelas akan muncul di sini</p>
                                </div>
                            <?php endif; ?>
                        </div>

                        <!-- BK Tab -->
                        <div class="tab-pane fade" id="bk" role="tabpanel">
                            <?php if (!empty($catatan_grouped['bk'])): ?>
                                <?php foreach ($catatan_grouped['bk'] as $catatan): ?>
                                    <?php include __DIR__ . '/components/catatan_card.php'; ?>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <div class="text-center py-4">
                                    <i class="bi bi-heart" style="font-size: 3rem; color: #dee2e6;"></i>
                                    <h6 class="text-muted mt-3">Belum ada catatan BK</h6>
                                    <p class="text-muted small">Catatan bimbingan konseling akan muncul di sini</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Absensi Section -->
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0 text-dark">
                            <i class="bi bi-calendar-check me-2"></i>
                            Data Absensi
                        </h5>
                        <?php if (Security::canEditSiswa($siswa['id_siswa'])): ?>
                        <a href="/siswa-app/public/absensi/add/<?= $siswa['id_siswa'] ?>" class="btn btn-primary btn-sm">
                            <i class="bi bi-plus-circle me-1"></i>
                            Tambah Absensi
                        </a>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="card-body">
                    <?php
                    // Get absensi data
                    require_once __DIR__ . '/../../models/Absensi.php';
                    $absensiModel = new Absensi();

                    // Get current month data
                    $currentMonth = date('Y-m-01');
                    $endMonth = date('Y-m-t');
                    $absensiList = $absensiModel->getAbsensiByStudent($siswa['id_siswa'], $currentMonth, $endMonth);
                    $absensiSummary = $absensiModel->getAbsensiSummary($siswa['id_siswa'], $currentMonth, $endMonth);
                    $attendancePercentage = $absensiModel->calculateAttendancePercentage($siswa['id_siswa'], $currentMonth, $endMonth);
                    ?>

                    <!-- Absensi Summary -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="text-center p-3 bg-light rounded">
                                <div class="text-success mb-2">
                                    <i class="bi bi-check-circle" style="font-size: 2rem;"></i>
                                </div>
                                <h4 class="mb-1 text-success"><?= number_format($attendancePercentage, 1) ?>%</h4>
                                <small class="text-muted">Kehadiran</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center p-3 bg-light rounded">
                                <div class="text-danger mb-2">
                                    <i class="bi bi-thermometer-half" style="font-size: 2rem;"></i>
                                </div>
                                <h4 class="mb-1 text-danger"><?= $absensiSummary['total_sakit'] ?></h4>
                                <small class="text-muted">Sakit</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center p-3 bg-light rounded">
                                <div class="text-warning mb-2">
                                    <i class="bi bi-file-text" style="font-size: 2rem;"></i>
                                </div>
                                <h4 class="mb-1 text-warning"><?= $absensiSummary['total_ijin'] ?></h4>
                                <small class="text-muted">Ijin</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center p-3 bg-light rounded">
                                <div class="text-secondary mb-2">
                                    <i class="bi bi-x-circle" style="font-size: 2rem;"></i>
                                </div>
                                <h4 class="mb-1 text-secondary"><?= $absensiSummary['total_alpha'] ?></h4>
                                <small class="text-muted">Alpha</small>
                            </div>
                        </div>
                    </div>

                    <!-- Absensi List -->
                    <?php if (!empty($absensiList)): ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Tanggal</th>
                                    <th>Jenis</th>
                                    <th>Keterangan</th>
                                    <th>Dicatat Oleh</th>
                                    <?php if (Security::canEditSiswa($siswa['id_siswa'])): ?>
                                    <th class="text-center">Aksi</th>
                                    <?php endif; ?>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($absensiList as $absen): ?>
                                <tr>
                                    <td>
                                        <strong><?= date('d/m/Y', strtotime($absen['tanggal'])) ?></strong>
                                        <br>
                                        <small class="text-muted"><?= date('l', strtotime($absen['tanggal'])) ?></small>
                                    </td>
                                    <td>
                                        <?php
                                        $jenisOptions = $absensiModel->getJenisKetidakhadiranOptions();
                                        $jenis = $jenisOptions[$absen['jenis_ketidakhadiran']];
                                        ?>
                                        <span class="badge bg-<?= $jenis['color'] ?> px-3 py-2">
                                            <i class="<?= $jenis['icon'] ?> me-1"></i>
                                            <?= $jenis['label'] ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?= htmlspecialchars($absen['keterangan'] ?: 'Tidak ada keterangan') ?>
                                        <?php if ($absen['surat_keterangan']): ?>
                                        <br>
                                        <small>
                                            <i class="bi bi-paperclip me-1"></i>
                                            <a href="/siswa-app/uploads/surat_keterangan/<?= $absen['surat_keterangan'] ?>"
                                               target="_blank" class="text-primary">
                                                Lihat Surat
                                            </a>
                                        </small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            <?= htmlspecialchars($absen['created_by_name']) ?>
                                            <br>
                                            <?= date('d/m/Y H:i', strtotime($absen['created_at'])) ?>
                                        </small>
                                    </td>
                                    <?php if (Security::canEditSiswa($siswa['id_siswa'])): ?>
                                    <td class="text-center">
                                        <div class="btn-group" role="group">
                                            <a href="/siswa-app/public/absensi/edit/<?= $absen['id'] ?>"
                                               class="btn btn-sm btn-outline-primary"
                                               title="Edit">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                            <button type="button"
                                                    class="btn btn-sm btn-outline-danger"
                                                    title="Hapus"
                                                    data-bs-toggle="modal"
                                                    data-bs-target="#deleteAbsensiModal"
                                                    data-absensi-id="<?= $absen['id'] ?>"
                                                    data-tanggal="<?= date('d/m/Y', strtotime($absen['tanggal'])) ?>">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                    <?php endif; ?>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <?php else: ?>
                    <div class="text-center py-5">
                        <div class="text-muted mb-3">
                            <i class="bi bi-calendar-check" style="font-size: 3rem;"></i>
                        </div>
                        <h5 class="text-muted">Belum Ada Data Absensi</h5>
                        <p class="text-muted">Siswa ini belum memiliki catatan ketidakhadiran bulan ini</p>
                        <?php if (Security::canEditSiswa($siswa['id_siswa'])): ?>
                        <a href="/siswa-app/public/absensi/add/<?= $siswa['id_siswa'] ?>" class="btn btn-primary">
                            <i class="bi bi-plus-circle me-1"></i>
                            Tambah Absensi Pertama
                        </a>
                        <?php endif; ?>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Absensi Modal -->
<?php if (Security::canEditSiswa($siswa['id_siswa'])): ?>
<div class="modal fade" id="deleteAbsensiModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title text-danger">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    Hapus Data Absensi
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="deleteAbsensiForm">
                <div class="modal-body">
                    <input type="hidden" name="csrf_token" value="<?= Security::generateCSRFToken() ?>">
                    <p>Apakah Anda yakin ingin menghapus data absensi tanggal <strong id="deleteAbsensiTanggal"></strong>?</p>
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        <strong>Peringatan:</strong> Tindakan ini tidak dapat dibatalkan!
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-danger">
                        <i class="bi bi-trash me-1"></i>
                        Hapus Absensi
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Modal Add Catatan -->
<div class="modal fade" id="addCatatanModal" tabindex="-1">
    <div class="modal-dialog modal-xl modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-plus-circle"></i>
                    Tambah Catatan Siswa
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="/siswa-app/public/catatan/create">
                <div class="modal-body">
                    <input type="hidden" name="csrf_token" value="<?= $csrf_token ?>">
                    <input type="hidden" name="siswa_id" value="<?= $siswa['id_siswa'] ?? $siswa['id'] ?>">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="jenis_catatan" class="form-label">Jenis Catatan <span class="text-danger">*</span></label>
                                <select class="form-select" id="jenis_catatan" name="jenis_catatan" required>
                                    <option value="">Pilih Jenis Catatan</option>
                                    <?php
                                    // Filter categories based on user role
                                    require_once __DIR__ . '/../../helpers/Security.php';
                                    $filteredCategories = Security::filterCatatanCategoriesByRole($catatan_categories ?? []);
                                    ?>
                                    <?php if (!empty($filteredCategories)): ?>
                                        <?php if (isset($filteredCategories['pamong'])): ?>
                                        <optgroup label="Pamong">
                                            <?php foreach ($filteredCategories['pamong'] as $cat): ?>
                                                <option value="<?= $cat['kode_kategori'] ?>"><?= $cat['nama_kategori'] ?></option>
                                            <?php endforeach; ?>
                                        </optgroup>
                                        <?php endif; ?>
                                        <?php if (isset($filteredCategories['wali_kelas'])): ?>
                                        <optgroup label="Wali Kelas">
                                            <?php foreach ($filteredCategories['wali_kelas'] as $cat): ?>
                                                <option value="<?= $cat['kode_kategori'] ?>"><?= $cat['nama_kategori'] ?></option>
                                            <?php endforeach; ?>
                                        </optgroup>
                                        <?php endif; ?>
                                        <?php if (isset($filteredCategories['bk'])): ?>
                                        <optgroup label="BK">
                                            <?php foreach ($filteredCategories['bk'] as $cat): ?>
                                                <option value="<?= $cat['kode_kategori'] ?>"><?= $cat['nama_kategori'] ?></option>
                                            <?php endforeach; ?>
                                        </optgroup>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="tanggal_catatan" class="form-label">Tanggal <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="tanggal_catatan" name="tanggal_catatan"
                                       value="<?= date('Y-m-d') ?>" required>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="tingkat_prioritas" class="form-label">Prioritas</label>
                                <select class="form-select" id="tingkat_prioritas" name="tingkat_prioritas">
                                    <option value="rendah">Rendah</option>
                                    <option value="sedang" selected>Sedang</option>
                                    <option value="tinggi">Tinggi</option>
                                    <option value="urgent">Urgent</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="status_catatan" class="form-label">Status</label>
                                <select class="form-select" id="status_catatan" name="status_catatan">
                                    <option value="draft">Draft</option>
                                    <option value="aktif" selected>Aktif</option>
                                    <option value="selesai">Selesai</option>
                                    <option value="ditunda">Ditunda</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="judul_catatan" class="form-label">Judul Catatan <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="judul_catatan" name="judul_catatan" required>
                    </div>

                    <div class="mb-3">
                        <label for="isi_catatan" class="form-label">Isi Catatan <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="isi_catatan" name="isi_catatan" rows="5" required></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="tindak_lanjut" class="form-label">Tindak Lanjut</label>
                        <textarea class="form-control" id="tindak_lanjut" name="tindak_lanjut" rows="3"></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="tanggal_tindak_lanjut" class="form-label">Tanggal Tindak Lanjut</label>
                        <input type="date" class="form-control" id="tanggal_tindak_lanjut" name="tanggal_tindak_lanjut">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-save"></i>
                        Simpan Catatan
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Upload Foto -->
<div class="modal fade" id="uploadFotoModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-camera-fill"></i>
                    <?= !empty($siswa['foto']) ? 'Ganti Foto Siswa' : 'Upload Foto Siswa' ?>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="/siswa-app/public/siswa/upload-foto/<?= $siswa['id_siswa'] ?? $siswa['id'] ?>"
                  enctype="multipart/form-data" id="uploadFotoForm">
                <div class="modal-body">
                    <input type="hidden" name="csrf_token" value="<?= $csrf_token ?? Security::generateCSRFToken() ?>">

                    <div class="mb-3">
                        <label for="foto_siswa" class="form-label">
                            <i class="bi bi-image"></i> Pilih Foto <span class="text-danger">*</span>
                        </label>
                        <input type="file"
                               class="form-control"
                               id="foto_siswa"
                               name="foto_siswa"
                               accept="image/jpeg,image/jpg,image/png,image/gif"
                               required>
                        <div class="form-text">
                            <small class="text-muted">
                                <i class="bi bi-info-circle"></i>
                                Format yang didukung: JPG, JPEG, PNG, GIF. Maksimal 2MB.
                            </small>
                        </div>
                    </div>

                    <!-- Preview Area -->
                    <div class="mb-3" id="previewArea" style="display: none;">
                        <label class="form-label">Preview:</label>
                        <div class="text-center">
                            <img id="imagePreview"
                                 src=""
                                 alt="Preview"
                                 class="img-fluid rounded-circle shadow"
                                 style="width: 150px; height: 150px; object-fit: cover;">
                        </div>
                    </div>

                    <?php if (!empty($siswa['foto'])): ?>
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle"></i>
                        <strong>Foto saat ini:</strong> Foto yang ada akan diganti dengan foto baru yang Anda upload.
                    </div>
                    <?php endif; ?>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary" id="uploadBtn">
                        <i class="bi bi-upload"></i>
                        Upload Foto
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Preview image before upload
document.getElementById('foto_siswa').addEventListener('change', function(e) {
    const file = e.target.files[0];
    const previewArea = document.getElementById('previewArea');
    const imagePreview = document.getElementById('imagePreview');

    if (file) {
        // Validate file type
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
        if (!allowedTypes.includes(file.type)) {
            alert('Format file tidak didukung. Gunakan JPG, JPEG, PNG, atau GIF.');
            e.target.value = '';
            previewArea.style.display = 'none';
            return;
        }

        // Validate file size (2MB)
        if (file.size > 2 * 1024 * 1024) {
            alert('Ukuran file terlalu besar. Maksimal 2MB.');
            e.target.value = '';
            previewArea.style.display = 'none';
            return;
        }

        // Show preview
        const reader = new FileReader();
        reader.onload = function(e) {
            imagePreview.src = e.target.result;
            previewArea.style.display = 'block';
        };
        reader.readAsDataURL(file);
    } else {
        previewArea.style.display = 'none';
    }
});

// Handle form submission
document.getElementById('uploadFotoForm').addEventListener('submit', function(e) {
    const fileInput = document.getElementById('foto_siswa');
    const uploadBtn = document.getElementById('uploadBtn');

    if (!fileInput.files[0]) {
        e.preventDefault();
        alert('Silakan pilih foto terlebih dahulu.');
        return;
    }

    // Show loading state
    uploadBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> Uploading...';
    uploadBtn.disabled = true;
});

// Delete foto function
function deleteFoto(siswaId) {
    if (confirm('Apakah Anda yakin ingin menghapus foto siswa ini?')) {
        // Create form for delete request
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '/siswa-app/public/siswa/delete-foto/' + siswaId;

        // Add CSRF token
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = 'csrf_token';
        csrfInput.value = '<?= $csrf_token ?? Security::generateCSRFToken() ?>';
        form.appendChild(csrfInput);

        // Submit form
        document.body.appendChild(form);
        form.submit();
    }
}
</script>

<style>
/* Custom colors for professional look */
.bg-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* Alternative soft gradients - pilih salah satu */
.bg-gradient-soft {
    /* Option 1: Soft Gray - Professional dan netral */
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);

    /* Option 2: Soft Blue - Tenang dan trustworthy */
    /* background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%); */

    /* Option 3: Soft Teal - Modern dan fresh */
    /* background: linear-gradient(135deg, #00b894 0%, #00a085 100%); */

    /* Option 4: Soft Navy - Elegant dan professional */
    /* background: linear-gradient(135deg, #2d3436 0%, #636e72 100%); */
}

.text-purple {
    color: #6f42c1 !important;
}

.bg-purple {
    background-color: #6f42c1 !important;
}

.text-pink {
    color: #e91e63 !important;
}

.bg-pink {
    background-color: #e91e63 !important;
}

/* Card hover effects */
.card {
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1) !important;
}

/* Info groups styling */
.info-group {
    position: relative;
}

.info-group::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background: #007bff;
    border-radius: 2px;
}

.info-group h6 {
    padding-left: 15px;
}

/* Parent info styling */
.parent-info {
    position: relative;
    padding-left: 15px;
}

.parent-info::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #e9ecef;
    border-radius: 1px;
}

/* File item hover */
.bg-light:hover {
    background-color: #f8f9fa !important;
    transform: translateX(5px);
    transition: all 0.2s ease;
}

/* Badge styling */
.badge {
    font-weight: 500;
    letter-spacing: 0.5px;
}

/* Button improvements */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.btn:hover {
    transform: translateY(-1px);
}

/* Responsive improvements */
@media (max-width: 768px) {
    .card-body {
        padding: 1.5rem !important;
    }

    .info-group h6 {
        font-size: 14px;
    }

    .parent-info {
        margin-bottom: 2rem;
    }
}

/* Photo section improvements */
.bg-white img {
    border: 4px solid #f8f9fa !important;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

/* Quick actions improvements */
.d-grid .btn {
    text-align: left;
    justify-content: flex-start;
}

.d-grid .btn i {
    width: 20px;
}

/* Clean white theme improvements */
.card-body.p-0 .row.g-0 {
    background: white;
    border-radius: 0.5rem;
    overflow: hidden;
}

/* Photo section border */
.col-md-4.bg-white {
    position: relative;
}

.col-md-4.bg-white::after {
    content: '';
    position: absolute;
    right: 0;
    top: 20px;
    bottom: 20px;
    width: 1px;
    background: linear-gradient(to bottom, transparent, #e9ecef, transparent);
}

/* Clean card styling */
.card.shadow-lg {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.08) !important;
    border: 1px solid #f1f3f4;
}

/* Improved spacing for white theme */
.bg-white .text-center.p-4 {
    padding: 2rem !important;
}

@media (max-width: 768px) {
    .col-md-4.bg-white::after {
        display: none;
    }

    .col-md-4.bg-white {
        border-right: none !important;
        border-bottom: 1px solid #e9ecef;
    }
}

/* Modal improvements */
.modal-xl {
    max-width: 90vw;
}

.modal-dialog-scrollable {
    max-height: 90vh;
}

.modal-dialog-scrollable .modal-content {
    max-height: 90vh;
    overflow: hidden;
}

.modal-dialog-scrollable .modal-body {
    overflow-y: auto;
    max-height: calc(90vh - 120px); /* Account for header and footer */
}

/* Form improvements in modal */
#addCatatanModal .form-control,
#addCatatanModal .form-select {
    border-radius: 6px;
    border: 1px solid #dee2e6;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

#addCatatanModal .form-control:focus,
#addCatatanModal .form-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

#addCatatanModal .form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

#addCatatanModal .text-danger {
    color: #dc3545 !important;
}

/* Responsive modal adjustments */
@media (max-width: 1200px) {
    .modal-xl {
        max-width: 95vw;
    }
}

@media (max-width: 768px) {
    .modal-xl {
        max-width: 98vw;
        margin: 0.5rem;
    }

    .modal-dialog-scrollable {
        max-height: 95vh;
    }

    .modal-dialog-scrollable .modal-body {
        max-height: calc(95vh - 120px);
        padding: 1rem;
    }

    #addCatatanModal .row .col-md-6 {
        margin-bottom: 1rem;
    }
}

/* Better spacing for form elements */
#addCatatanModal .mb-3 {
    margin-bottom: 1.5rem !important;
}

#addCatatanModal .row {
    margin-bottom: 0.5rem;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Delete Absensi Modal
    const deleteAbsensiModal = document.getElementById('deleteAbsensiModal');
    if (deleteAbsensiModal) {
        deleteAbsensiModal.addEventListener('show.bs.modal', function(event) {
            const button = event.relatedTarget;
            const absensiId = button.getAttribute('data-absensi-id');
            const tanggal = button.getAttribute('data-tanggal');

            document.getElementById('deleteAbsensiTanggal').textContent = tanggal;
            document.getElementById('deleteAbsensiForm').action = '/siswa-app/public/absensi/delete/' + absensiId;
        });
    }
});
</script>