<?php
// Script untuk update struktur kelas sesuai dengan sistem sekolah
// File: update_kelas_structure.php

require_once __DIR__ . '/app/models/Database.php';

try {
    echo "<h2>🏫 Updating Kelas Structure...</h2>";
    
    $db = new Database();
    
    // 1. Hapus kelas yang tidak sesuai struktur
    echo "<p>1. Cleaning up existing classes...</p>";
    $db->query("DELETE FROM kelas WHERE nama_kelas NOT IN (
        'KPP-A', 'KPP-B', 'KPP-C', 
        'X-1', 'X-2', 
        'XI-1', 'XI-2', 
        'XII-1', 'XII-2', 
        'KPA'
    )");
    echo "<p style='color: green;'>✅ Cleaned up non-standard classes</p>";
    
    // 2. Insert/Update kelas sesuai struktur yang benar
    echo "<p>2. Creating standard class structure...</p>";
    
    $standardKelas = [
        // KPP Classes
        ['KPP-A', 'KPP', 'Umum'],
        ['KPP-B', 'KPP', 'Umum'],
        ['KPP-C', 'KPP', 'Umum'],
        
        // X Classes
        ['X-1', 'X', 'Umum'],
        ['X-2', 'X', 'Umum'],
        
        // XI Classes
        ['XI-1', 'XI', 'Umum'],
        ['XI-2', 'XI', 'Umum'],
        
        // XII Classes
        ['XII-1', 'XII', 'Umum'],
        ['XII-2', 'XII', 'Umum'],
        
        // KPA Class
        ['KPA', 'KPA', 'Umum']
    ];
    
    foreach ($standardKelas as $kelas) {
        try {
            // Check if class already exists
            $existing = $db->fetch("SELECT id FROM kelas WHERE nama_kelas = ?", [$kelas[0]]);
            
            if (!$existing) {
                // Insert new class
                $db->query(
                    "INSERT INTO kelas (nama_kelas, tingkat, jurusan, tahun_pelajaran, is_active, created_by, created_at) VALUES (?, ?, ?, '2024/2025', 1, 1, NOW())",
                    $kelas
                );
                echo "<p style='color: green;'>✅ Created class: {$kelas[0]} ({$kelas[1]} - {$kelas[2]})</p>";
            } else {
                // Update existing class
                $db->query(
                    "UPDATE kelas SET tingkat = ?, jurusan = ?, is_active = 1, updated_at = NOW() WHERE nama_kelas = ?",
                    [$kelas[1], $kelas[2], $kelas[0]]
                );
                echo "<p style='color: blue;'>🔄 Updated class: {$kelas[0]} ({$kelas[1]} - {$kelas[2]})</p>";
            }
        } catch (Exception $e) {
            echo "<p style='color: orange;'>⚠️ Class {$kelas[0]} might already exist or error: " . $e->getMessage() . "</p>";
        }
    }
    
    // 3. Update siswa yang ada untuk menggunakan kelas yang benar
    echo "<p>3. Updating student class assignments...</p>";
    
    // Map old class names to new ones if needed
    $classMapping = [
        'KPP-A' => 'KPP-A',
        'KPP-B' => 'KPP-B', 
        'KPP-C' => 'KPP-C',
        'X-IPA-1' => 'X-1',
        'X-IPA-2' => 'X-2',
        'X-IPS-1' => 'X-1',
        'X-IPS-2' => 'X-2',
        'XI-IPA-1' => 'XI-1',
        'XI-IPA-2' => 'XI-2',
        'XI-IPS-1' => 'XI-1',
        'XI-IPS-2' => 'XI-2',
        'XII-IPA-1' => 'XII-1',
        'XII-IPS-1' => 'XII-2',
        'KPA-A' => 'KPA',
        'KPA-B' => 'KPA'
    ];
    
    foreach ($classMapping as $oldName => $newName) {
        if ($oldName !== $newName) {
            $oldKelas = $db->fetch("SELECT id FROM kelas WHERE nama_kelas = ?", [$oldName]);
            $newKelas = $db->fetch("SELECT id FROM kelas WHERE nama_kelas = ?", [$newName]);
            
            if ($oldKelas && $newKelas) {
                // Update students to use new class
                $db->query(
                    "UPDATE siswa SET kelas_id = ? WHERE kelas_id = ?",
                    [$newKelas['id'], $oldKelas['id']]
                );
                echo "<p style='color: blue;'>🔄 Moved students from {$oldName} to {$newName}</p>";
                
                // Delete old class
                $db->query("DELETE FROM kelas WHERE id = ?", [$oldKelas['id']]);
                echo "<p style='color: orange;'>🗑️ Removed old class: {$oldName}</p>";
            }
        }
    }
    
    // 4. Add sample students for each class if needed
    echo "<p>4. Adding sample students for each class...</p>";
    
    $sampleStudents = [
        // KPP Students
        ['2024001', 'Ahmad Fauzi', 'L', 'KPP-A'],
        ['2024002', 'Siti Nurhaliza', 'P', 'KPP-A'],
        ['2024003', 'Budi Santoso', 'L', 'KPP-B'],
        ['2024004', 'Dewi Sartika', 'P', 'KPP-B'],
        ['2024005', 'Rizki Pratama', 'L', 'KPP-C'],
        ['2024006', 'Maya Sari', 'P', 'KPP-C'],
        
        // X Students
        ['2024007', 'Andi Wijaya', 'L', 'X-1'],
        ['2024008', 'Fitri Handayani', 'P', 'X-1'],
        ['2024009', 'Doni Setiawan', 'L', 'X-2'],
        ['2024010', 'Lina Marlina', 'P', 'X-2'],
        
        // XI Students
        ['2024011', 'Agus Salim', 'L', 'XI-1'],
        ['2024012', 'Rina Susanti', 'P', 'XI-1'],
        ['2024013', 'Eko Prasetyo', 'L', 'XI-2'],
        ['2024014', 'Indah Permata', 'P', 'XI-2'],
        
        // XII Students
        ['2024015', 'Hendra Gunawan', 'L', 'XII-1'],
        ['2024016', 'Sari Dewi', 'P', 'XII-1'],
        ['2024017', 'Yusuf Rahman', 'L', 'XII-2'],
        ['2024018', 'Nita Anggraini', 'P', 'XII-2'],
        
        // KPA Students
        ['2024019', 'Fajar Sidiq', 'L', 'KPA'],
        ['2024020', 'Wulan Dari', 'P', 'KPA']
    ];
    
    // Get kelas IDs
    $kelasMap = [];
    $allKelas = $db->fetchAll("SELECT id, nama_kelas FROM kelas WHERE is_active = 1");
    foreach ($allKelas as $kelas) {
        $kelasMap[$kelas['nama_kelas']] = $kelas['id'];
    }
    
    foreach ($sampleStudents as $siswa) {
        if (isset($kelasMap[$siswa[3]])) {
            try {
                // Check if student already exists
                $existing = $db->fetch("SELECT id_siswa FROM siswa WHERE nis = ?", [$siswa[0]]);
                
                if (!$existing) {
                    $db->query(
                        "INSERT INTO siswa (nis, nama_lengkap, jenis_kelamin, kelas_id, tahun_masuk, status_siswa, created_by, created_at) VALUES (?, ?, ?, ?, 2024, 'aktif', 1, NOW())",
                        [$siswa[0], $siswa[1], $siswa[2], $kelasMap[$siswa[3]]]
                    );
                    echo "<p style='color: green;'>✅ Added student: {$siswa[1]} ({$siswa[0]}) - {$siswa[3]}</p>";
                } else {
                    echo "<p style='color: orange;'>⚠️ Student {$siswa[0]} already exists</p>";
                }
            } catch (Exception $e) {
                echo "<p style='color: red;'>❌ Error adding student {$siswa[0]}: " . $e->getMessage() . "</p>";
            }
        }
    }
    
    // 5. Update wali kelas assignments
    echo "<p>5. Updating wali kelas assignments...</p>";
    
    // Get existing wali kelas users
    $waliKelasUsers = $db->fetchAll("SELECT id, username FROM users WHERE role = 'wali_kelas'");
    
    if (!empty($waliKelasUsers)) {
        $kelasNames = ['KPP-A', 'KPP-B', 'KPP-C', 'X-1', 'X-2', 'XI-1', 'XI-2', 'XII-1', 'XII-2', 'KPA'];
        
        foreach ($waliKelasUsers as $index => $wali) {
            if (isset($kelasNames[$index]) && isset($kelasMap[$kelasNames[$index]])) {
                $db->query(
                    "UPDATE kelas SET wali_kelas_id = ? WHERE id = ?",
                    [$wali['id'], $kelasMap[$kelasNames[$index]]]
                );
                echo "<p style='color: blue;'>🔄 Assigned {$wali['username']} as wali kelas for {$kelasNames[$index]}</p>";
            }
        }
    }
    
    // 6. Verification
    echo "<h3>🔍 Verification Results:</h3>";
    
    $finalKelas = $db->fetchAll("
        SELECT 
            k.nama_kelas, 
            k.tingkat, 
            k.jurusan,
            COUNT(s.id_siswa) as jumlah_siswa,
            u.nama_lengkap as wali_kelas
        FROM kelas k 
        LEFT JOIN siswa s ON k.id = s.kelas_id AND s.status_siswa = 'aktif'
        LEFT JOIN users u ON k.wali_kelas_id = u.id
        WHERE k.is_active = 1
        GROUP BY k.id, k.nama_kelas, k.tingkat, k.jurusan, u.nama_lengkap
        ORDER BY 
            CASE k.tingkat 
                WHEN 'KPP' THEN 1 
                WHEN 'X' THEN 2 
                WHEN 'XI' THEN 3 
                WHEN 'XII' THEN 4 
                WHEN 'KPA' THEN 5 
                ELSE 6 
            END,
            k.nama_kelas
    ");
    
    echo "<table border='1' cellpadding='10' cellspacing='0' style='border-collapse: collapse; width: 100%; margin-top: 20px;'>";
    echo "<thead style='background-color: #f8f9fa;'>";
    echo "<tr>";
    echo "<th>Nama Kelas</th>";
    echo "<th>Tingkat</th>";
    echo "<th>Jurusan</th>";
    echo "<th>Jumlah Siswa</th>";
    echo "<th>Wali Kelas</th>";
    echo "</tr>";
    echo "</thead>";
    echo "<tbody>";
    
    foreach ($finalKelas as $kelas) {
        echo "<tr>";
        echo "<td><strong>{$kelas['nama_kelas']}</strong></td>";
        echo "<td>{$kelas['tingkat']}</td>";
        echo "<td>{$kelas['jurusan']}</td>";
        echo "<td>{$kelas['jumlah_siswa']} siswa</td>";
        echo "<td>" . ($kelas['wali_kelas'] ?? '<em>Belum ditentukan</em>') . "</td>";
        echo "</tr>";
    }
    
    echo "</tbody>";
    echo "</table>";
    
    // Summary
    $totalKelas = count($finalKelas);
    $totalSiswa = array_sum(array_column($finalKelas, 'jumlah_siswa'));
    
    echo "<div style='margin-top: 20px; padding: 20px; background-color: #d4edda; border: 1px solid #c3e6cb; border-radius: 5px;'>";
    echo "<h4 style='color: #155724; margin-bottom: 10px;'>📊 Summary:</h4>";
    echo "<ul style='color: #155724;'>";
    echo "<li><strong>Total Kelas:</strong> {$totalKelas} kelas</li>";
    echo "<li><strong>Total Siswa:</strong> {$totalSiswa} siswa</li>";
    echo "<li><strong>Struktur:</strong> KPP (3 kelas), X (2 kelas), XI (2 kelas), XII (2 kelas), KPA (1 kelas)</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h2 style='color: green; margin-top: 30px;'>🎉 Kelas structure update completed successfully!</h2>";
    
} catch (Exception $e) {
    echo "<h2 style='color: red;'>❌ Error updating kelas structure:</h2>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 20px;
    background-color: #f8f9fa;
}

h2, h3 {
    color: #495057;
}

table {
    background-color: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border-radius: 8px;
    overflow: hidden;
}

th {
    background-color: #007bff !important;
    color: white !important;
    font-weight: 600;
}

td {
    padding: 12px !important;
}

tr:nth-child(even) {
    background-color: #f8f9fa;
}

tr:hover {
    background-color: #e9ecef;
}

p {
    margin: 5px 0;
    padding: 5px 10px;
    border-radius: 4px;
}

p[style*="green"] {
    background-color: #d4edda;
    border-left: 4px solid #28a745;
}

p[style*="blue"] {
    background-color: #cce7ff;
    border-left: 4px solid #007bff;
}

p[style*="orange"] {
    background-color: #fff3cd;
    border-left: 4px solid #ffc107;
}

p[style*="red"] {
    background-color: #f8d7da;
    border-left: 4px solid #dc3545;
}
</style>
