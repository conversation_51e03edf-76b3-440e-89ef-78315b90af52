<?php
// Component untuk menampilkan card catatan
// Variable $catatan harus tersedia dari parent

// Priority colors
$priorityColors = [
    'rendah' => 'success',
    'sedang' => 'info', 
    'tinggi' => 'warning',
    'urgent' => 'danger'
];

// Status colors
$statusColors = [
    'draft' => 'secondary',
    'aktif' => 'primary',
    'selesai' => 'success',
    'ditunda' => 'warning'
];

$priorityColor = $priorityColors[$catatan['tingkat_prioritas']] ?? 'secondary';
$statusColor = $statusColors[$catatan['status_catatan']] ?? 'secondary';
$badgeColor = $catatan['warna_badge'] ?? '#6c757d';
?>

<div class="card mb-3 border-start border-4" style="border-left-color: <?= $badgeColor ?> !important;">
    <div class="card-body">
        <div class="d-flex justify-content-between align-items-start mb-2">
            <div class="flex-grow-1">
                <div class="d-flex align-items-center mb-2">
                    <i class="<?= $catatan['icon_class'] ?? 'bi-note-text' ?> me-2" style="color: <?= $badgeColor ?>;"></i>
                    <h6 class="card-title mb-0">
                        <?= htmlspecialchars($catatan['judul_catatan']) ?>
                    </h6>
                </div>
                
                <div class="d-flex flex-wrap gap-2 mb-2">
                    <span class="badge" style="background-color: <?= $badgeColor ?>;">
                        <?= htmlspecialchars($catatan['nama_kategori']) ?>
                    </span>
                    <span class="badge bg-<?= $priorityColor ?>">
                        <?= ucfirst($catatan['tingkat_prioritas']) ?>
                    </span>
                    <span class="badge bg-<?= $statusColor ?>">
                        <?= ucfirst($catatan['status_catatan']) ?>
                    </span>
                </div>
            </div>
            
            <div class="dropdown">
                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                    <i class="bi bi-three-dots"></i>
                </button>
                <ul class="dropdown-menu">
                    <li>
                        <a class="dropdown-item" href="#" onclick="viewCatatan(<?= $catatan['id'] ?>)">
                            <i class="bi bi-eye"></i> Lihat Detail
                        </a>
                    </li>
                    <li>
                        <a class="dropdown-item" href="#" onclick="editCatatan(<?= $catatan['id'] ?>)">
                            <i class="bi bi-pencil"></i> Edit
                        </a>
                    </li>
                    <li><hr class="dropdown-divider"></li>
                    <li>
                        <a class="dropdown-item text-danger" href="#" onclick="deleteCatatan(<?= $catatan['id'] ?>)">
                            <i class="bi bi-trash"></i> Hapus
                        </a>
                    </li>
                </ul>
            </div>
        </div>
        
        <div class="card-text">
            <p class="mb-2">
                <?= nl2br(htmlspecialchars(substr($catatan['isi_catatan'], 0, 200))) ?>
                <?php if (strlen($catatan['isi_catatan']) > 200): ?>
                    <span class="text-muted">...</span>
                    <a href="#" onclick="viewCatatan(<?= $catatan['id'] ?>)" class="text-primary">Baca selengkapnya</a>
                <?php endif; ?>
            </p>
            
            <?php if (!empty($catatan['tindak_lanjut'])): ?>
                <div class="alert alert-light py-2 mb-2">
                    <small class="text-muted">
                        <i class="bi bi-arrow-right-circle"></i>
                        <strong>Tindak Lanjut:</strong>
                        <?= htmlspecialchars(substr($catatan['tindak_lanjut'], 0, 100)) ?>
                        <?php if (strlen($catatan['tindak_lanjut']) > 100): ?>...<?php endif; ?>
                    </small>
                </div>
            <?php endif; ?>
        </div>
        
        <div class="d-flex justify-content-between align-items-center">
            <div class="text-muted small">
                <i class="bi bi-calendar3"></i>
                <?= date('d M Y', strtotime($catatan['tanggal_catatan'])) ?>
                
                <?php if (!empty($catatan['tanggal_tindak_lanjut'])): ?>
                    <span class="ms-3">
                        <i class="bi bi-clock"></i>
                        Tindak lanjut: <?= date('d M Y', strtotime($catatan['tanggal_tindak_lanjut'])) ?>
                    </span>
                <?php endif; ?>
            </div>
            
            <div class="text-muted small">
                <i class="bi bi-person"></i>
                <?= htmlspecialchars($catatan['created_by_name'] ?? 'Unknown') ?>
            </div>
        </div>
    </div>
</div>

<script>
function viewCatatan(id) {
    // Load catatan detail in modal
    fetch(`/siswa-app/public/catatan/detail/${id}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showCatatanDetail(data.catatan);
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Terjadi kesalahan saat memuat detail catatan');
        });
}

function editCatatan(id) {
    // Redirect to edit page or load in modal
    window.location.href = `/siswa-app/public/catatan/edit/${id}`;
}

function deleteCatatan(id) {
    if (confirm('Yakin ingin menghapus catatan ini?')) {
        fetch(`/siswa-app/public/catatan/delete/${id}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-Token': '<?= $csrf_token ?? '' ?>'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Terjadi kesalahan saat menghapus catatan');
        });
    }
}

function showCatatanDetail(catatan) {
    // Create and show detail modal
    const modalHtml = `
        <div class="modal fade" id="catatanDetailModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="${catatan.icon_class}"></i>
                            ${catatan.judul_catatan}
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <strong>Kategori:</strong> ${catatan.nama_kategori}<br>
                                <strong>Tanggal:</strong> ${new Date(catatan.tanggal_catatan).toLocaleDateString('id-ID')}<br>
                                <strong>Prioritas:</strong> <span class="badge bg-${getPriorityColor(catatan.tingkat_prioritas)}">${catatan.tingkat_prioritas}</span>
                            </div>
                            <div class="col-md-6">
                                <strong>Status:</strong> <span class="badge bg-${getStatusColor(catatan.status_catatan)}">${catatan.status_catatan}</span><br>
                                <strong>Dibuat oleh:</strong> ${catatan.created_by_name}<br>
                                <strong>Dibuat:</strong> ${new Date(catatan.created_at).toLocaleDateString('id-ID')}
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <strong>Isi Catatan:</strong>
                            <div class="mt-2 p-3 bg-light rounded">
                                ${catatan.isi_catatan.replace(/\n/g, '<br>')}
                            </div>
                        </div>
                        
                        ${catatan.tindak_lanjut ? `
                            <div class="mb-3">
                                <strong>Tindak Lanjut:</strong>
                                <div class="mt-2 p-3 bg-warning bg-opacity-10 rounded">
                                    ${catatan.tindak_lanjut.replace(/\n/g, '<br>')}
                                </div>
                                ${catatan.tanggal_tindak_lanjut ? `
                                    <small class="text-muted">
                                        <i class="bi bi-clock"></i>
                                        Target: ${new Date(catatan.tanggal_tindak_lanjut).toLocaleDateString('id-ID')}
                                    </small>
                                ` : ''}
                            </div>
                        ` : ''}
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
                        <button type="button" class="btn btn-primary" onclick="editCatatan(${catatan.id})">
                            <i class="bi bi-pencil"></i> Edit
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // Remove existing modal if any
    const existingModal = document.getElementById('catatanDetailModal');
    if (existingModal) {
        existingModal.remove();
    }
    
    // Add modal to body and show
    document.body.insertAdjacentHTML('beforeend', modalHtml);
    const modal = new bootstrap.Modal(document.getElementById('catatanDetailModal'));
    modal.show();
}

function getPriorityColor(priority) {
    const colors = {
        'rendah': 'success',
        'sedang': 'info',
        'tinggi': 'warning', 
        'urgent': 'danger'
    };
    return colors[priority] || 'secondary';
}

function getStatusColor(status) {
    const colors = {
        'draft': 'secondary',
        'aktif': 'primary',
        'selesai': 'success',
        'ditunda': 'warning'
    };
    return colors[status] || 'secondary';
}
</script>
