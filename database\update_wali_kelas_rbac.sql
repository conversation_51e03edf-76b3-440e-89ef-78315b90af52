-- Update database untuk implementasi RBAC Wali Kelas
-- File: database/update_wali_kelas_rbac.sql

-- 1. Update enum role untuk menambah wali_kelas
ALTER TABLE users MODIFY COLUMN role ENUM('admin', 'guru', 'staff', 'wali_kelas') DEFAULT 'staff';

-- 2. Tambah kolom wali_kelas_id ke tabel kelas
ALTER TABLE kelas ADD COLUMN wali_kelas_id INT NULL AFTER wali_kelas;

-- 3. Tambah foreign key constraint
ALTER TABLE kelas ADD CONSTRAINT fk_kelas_wali_kelas 
FOREIGN KEY (wali_kelas_id) REFERENCES users(id) ON DELETE SET NULL;

-- 4. Tambah index untuk performance
ALTER TABLE kelas ADD INDEX idx_wali_kelas_id (wali_kelas_id);

-- 5. Insert sample wali kelas users
INSERT INTO users (username, email, password, role, nama_lengkap, is_active) VALUES 
('wali_x_ipa1', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'wali_kelas', 'Budi Santoso', TRUE),
('wali_x_ips1', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'wali_kelas', 'Siti Aminah', TRUE),
('wali_xi_ipa1', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'wali_kelas', 'Ahmad Rahman', TRUE);
-- Password untuk semua: password

-- 6. Update kelas dengan wali_kelas_id yang sesuai
UPDATE kelas SET wali_kelas_id = (
    SELECT id FROM users WHERE nama_lengkap = kelas.wali_kelas AND role = 'wali_kelas' LIMIT 1
) WHERE wali_kelas IS NOT NULL;

-- 7. Tambah tabel untuk mapping user ke kelas (jika satu user bisa jadi wali beberapa kelas)
CREATE TABLE IF NOT EXISTS user_kelas_mapping (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    kelas_id INT NOT NULL,
    role_type ENUM('wali_kelas', 'guru_mapel', 'guru_piket') DEFAULT 'wali_kelas',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (kelas_id) REFERENCES kelas(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_kelas_role (user_id, kelas_id, role_type),
    INDEX idx_user_id (user_id),
    INDEX idx_kelas_id (kelas_id),
    INDEX idx_role_type (role_type)
);

-- 8. Insert mapping untuk wali kelas yang sudah ada
INSERT INTO user_kelas_mapping (user_id, kelas_id, role_type)
SELECT wali_kelas_id, id, 'wali_kelas'
FROM kelas 
WHERE wali_kelas_id IS NOT NULL;

-- 9. Update kategori catatan untuk menambah validasi role
-- Tambah kolom allowed_roles ke tabel kategori_catatan
ALTER TABLE kategori_catatan ADD COLUMN allowed_roles JSON DEFAULT NULL AFTER is_active;

-- 10. Update kategori catatan dengan allowed roles
UPDATE kategori_catatan SET allowed_roles = '["admin", "guru"]' WHERE kode_kategori LIKE 'pamong_%';
UPDATE kategori_catatan SET allowed_roles = '["admin", "wali_kelas"]' WHERE kode_kategori LIKE 'wali_%';
UPDATE kategori_catatan SET allowed_roles = '["admin", "guru"]' WHERE kode_kategori LIKE 'bk_%';

-- 11. Tambah beberapa sample data untuk testing
-- Update kelas dengan wali_kelas_id
UPDATE kelas SET wali_kelas_id = (SELECT id FROM users WHERE username = 'wali_x_ipa1') WHERE nama_kelas = 'X-IPA-1';
UPDATE kelas SET wali_kelas_id = (SELECT id FROM users WHERE username = 'wali_x_ips1') WHERE nama_kelas = 'X-IPS-1';
UPDATE kelas SET wali_kelas_id = (SELECT id FROM users WHERE username = 'wali_xi_ipa1') WHERE nama_kelas = 'XI-IPA-1';

-- 12. Tambah view untuk kemudahan query
CREATE OR REPLACE VIEW view_kelas_with_wali AS
SELECT 
    k.id,
    k.nama_kelas,
    k.tingkat,
    k.jurusan,
    k.tahun_pelajaran,
    k.wali_kelas as wali_kelas_nama,
    k.wali_kelas_id,
    u.username as wali_kelas_username,
    u.email as wali_kelas_email,
    u.nama_lengkap as wali_kelas_nama_lengkap,
    k.kapasitas,
    k.is_active,
    k.created_at,
    k.updated_at,
    (SELECT COUNT(*) FROM siswa WHERE kelas_id = k.id AND status_siswa = 'aktif') as jumlah_siswa
FROM kelas k
LEFT JOIN users u ON k.wali_kelas_id = u.id
WHERE k.is_active = 1;

-- 13. Tambah view untuk siswa dengan info kelas dan wali kelas
CREATE OR REPLACE VIEW view_siswa_with_kelas_wali AS
SELECT 
    s.*,
    k.nama_kelas,
    k.tingkat,
    k.jurusan,
    k.tahun_pelajaran,
    k.wali_kelas_id,
    u.username as wali_kelas_username,
    u.nama_lengkap as wali_kelas_nama_lengkap
FROM siswa s
LEFT JOIN kelas k ON s.kelas_id = k.id
LEFT JOIN users u ON k.wali_kelas_id = u.id
WHERE s.status_siswa = 'aktif';

-- 14. Tambah stored procedure untuk cek akses wali kelas
DELIMITER //
CREATE OR REPLACE FUNCTION can_wali_kelas_access_siswa(
    p_user_id INT,
    p_siswa_id INT
) RETURNS BOOLEAN
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE v_count INT DEFAULT 0;
    
    -- Cek apakah user adalah wali kelas dari siswa tersebut
    SELECT COUNT(*) INTO v_count
    FROM siswa s
    JOIN kelas k ON s.kelas_id = k.id
    WHERE s.id_siswa = p_siswa_id 
    AND k.wali_kelas_id = p_user_id
    AND s.status_siswa = 'aktif'
    AND k.is_active = 1;
    
    RETURN v_count > 0;
END //
DELIMITER ;

-- 15. Tambah stored procedure untuk get siswa by wali kelas
DELIMITER //
CREATE OR REPLACE PROCEDURE get_siswa_by_wali_kelas(
    IN p_user_id INT
)
BEGIN
    SELECT s.*, k.nama_kelas, k.tingkat, k.jurusan
    FROM siswa s
    JOIN kelas k ON s.kelas_id = k.id
    WHERE k.wali_kelas_id = p_user_id
    AND s.status_siswa = 'aktif'
    AND k.is_active = 1
    ORDER BY s.nama_lengkap;
END //
DELIMITER ;

-- 16. Tambah trigger untuk auto-update user_kelas_mapping saat kelas diupdate
DELIMITER //
CREATE OR REPLACE TRIGGER tr_kelas_wali_update
AFTER UPDATE ON kelas
FOR EACH ROW
BEGIN
    -- Hapus mapping lama jika wali kelas berubah
    IF OLD.wali_kelas_id != NEW.wali_kelas_id THEN
        DELETE FROM user_kelas_mapping 
        WHERE kelas_id = NEW.id AND role_type = 'wali_kelas';
        
        -- Tambah mapping baru jika ada wali kelas baru
        IF NEW.wali_kelas_id IS NOT NULL THEN
            INSERT INTO user_kelas_mapping (user_id, kelas_id, role_type)
            VALUES (NEW.wali_kelas_id, NEW.id, 'wali_kelas')
            ON DUPLICATE KEY UPDATE is_active = TRUE;
        END IF;
    END IF;
END //
DELIMITER ;

-- 17. Tambah beberapa index untuk performance
CREATE INDEX idx_siswa_kelas_status ON siswa(kelas_id, status_siswa);
CREATE INDEX idx_catatan_siswa_jenis ON catatan_siswa(siswa_id, jenis_catatan);
CREATE INDEX idx_users_role_active ON users(role, is_active);

-- 18. Tambah sample data untuk testing
-- Insert beberapa siswa ke kelas yang sudah ada
INSERT INTO siswa (nis, nama_lengkap, jenis_kelamin, kelas_id, tahun_masuk, status_siswa, created_by) VALUES
('2024001', 'Ahmad Fauzi', 'L', 1, 2024, 'aktif', 1),
('2024002', 'Siti Nurhaliza', 'P', 1, 2024, 'aktif', 1),
('2024003', 'Budi Prasetyo', 'L', 2, 2024, 'aktif', 1),
('2024004', 'Dewi Sartika', 'P', 2, 2024, 'aktif', 1),
('2024005', 'Rizki Ramadhan', 'L', 3, 2024, 'aktif', 1);

-- 19. Verification queries (untuk testing)
-- SELECT 'Database update completed successfully' as status;
-- SELECT 'Users with wali_kelas role:' as info;
-- SELECT username, nama_lengkap FROM users WHERE role = 'wali_kelas';
-- SELECT 'Kelas with wali_kelas_id:' as info;
-- SELECT nama_kelas, wali_kelas, wali_kelas_id FROM kelas WHERE wali_kelas_id IS NOT NULL;
-- SELECT 'User-Kelas mapping:' as info;
-- SELECT u.nama_lengkap, k.nama_kelas FROM user_kelas_mapping ukm 
-- JOIN users u ON ukm.user_id = u.id 
-- JOIN kelas k ON ukm.kelas_id = k.id;
