<?php
require_once __DIR__ . '/../models/CatatanSiswa.php';
require_once __DIR__ . '/../helpers/Security.php';

class CatatanController {
    private $catatan;
    
    public function __construct() {
        $this->catatan = new CatatanSiswa();
    }
    
    /**
     * Show add catatan form
     */
    public function add($siswaId = null) {
        Security::requireAuth();

        // Validate siswa ID
        if (!$siswaId || !is_numeric($siswaId)) {
            $_SESSION['error'] = 'ID siswa tidak valid';
            header('Location: /siswa-app/public/siswa');
            exit;
        }

        try {
            // Get student info
            require_once __DIR__ . '/../models/Siswa.php';
            $siswaModel = new Siswa();
            $siswa = $siswaModel->getById($siswaId);

            if (!$siswa) {
                $_SESSION['error'] = 'Data siswa tidak ditemukan';
                header('Location: /siswa-app/public/siswa');
                exit;
            }

            // Check if user can access this student
            if (!Security::canAccessSiswa($siswaId)) {
                $_SESSION['error'] = 'Anda tidak memiliki akses ke data siswa ini';
                header('Location: /siswa-app/public/unauthorized');
                exit;
            }

            // Get categories for dropdown and filter by role
            $categories = $this->catatan->getCategoriesGrouped();
            $filteredCategories = Security::filterCatatanCategoriesByRole($categories);

            // Check if user has any categories available
            if (empty($filteredCategories)) {
                $_SESSION['error'] = 'Anda tidak memiliki akses untuk menambah catatan';
                header('Location: /siswa-app/public/siswa/detail/' . $siswaId);
                exit;
            }

            $data = [
                'title' => 'Tambah Catatan - ' . $siswa['nama_lengkap'],
                'siswa' => $siswa,
                'catatan_categories' => $filteredCategories,
                'csrf_token' => Security::generateCSRFToken()
            ];

            $this->view('catatan/add', $data);

        } catch (Exception $e) {
            error_log("Error in CatatanController::add(): " . $e->getMessage());
            $_SESSION['error'] = 'Terjadi kesalahan sistem. Silakan coba lagi.';
            header('Location: /siswa-app/public/siswa');
            exit;
        }
    }

    /**
     * Create new catatan
     */
    public function create() {
        Security::requireAuth();

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            try {
                // Validate CSRF token
                if (!Security::validateCSRFToken($_POST['csrf_token'] ?? '')) {
                    throw new Exception('Token keamanan tidak valid');
                }

                // Sanitize and validate form data
                $siswaId = filter_var($_POST['siswa_id'] ?? '', FILTER_VALIDATE_INT);
                if (!$siswaId) {
                    throw new Exception('ID siswa tidak valid');
                }

                // Check if user can access this student
                if (!Security::canAccessSiswa($siswaId)) {
                    throw new Exception('Anda tidak memiliki akses ke data siswa ini');
                }

                // Validate required fields
                $jenisCatatan = trim($_POST['jenis_catatan'] ?? '');
                $judulCatatan = trim($_POST['judul_catatan'] ?? '');
                $isiCatatan = trim($_POST['isi_catatan'] ?? '');

                if (empty($jenisCatatan)) {
                    throw new Exception('Jenis catatan harus dipilih');
                }
                if (empty($judulCatatan)) {
                    throw new Exception('Judul catatan harus diisi');
                }
                if (empty($isiCatatan)) {
                    throw new Exception('Isi catatan harus diisi');
                }

                // Validate date
                $tanggalCatatan = $_POST['tanggal_catatan'] ?? date('Y-m-d');
                if (!$this->validateDate($tanggalCatatan)) {
                    throw new Exception('Format tanggal tidak valid');
                }

                // Validate follow-up date if provided
                $tanggalTindakLanjut = $_POST['tanggal_tindak_lanjut'] ?? null;
                if (!empty($tanggalTindakLanjut) && !$this->validateDate($tanggalTindakLanjut)) {
                    throw new Exception('Format tanggal tindak lanjut tidak valid');
                }

                // Check if user can create this type of catatan
                if (!Security::canCreateCatatanType($jenisCatatan)) {
                    throw new Exception('Anda tidak memiliki akses untuk membuat jenis catatan ini');
                }

                // Prepare data
                $data = [
                    'siswa_id' => $siswaId,
                    'jenis_catatan' => $jenisCatatan,
                    'judul_catatan' => Security::sanitizeInput($judulCatatan),
                    'isi_catatan' => Security::sanitizeInput($isiCatatan),
                    'tanggal_catatan' => $tanggalCatatan,
                    'tingkat_prioritas' => $_POST['tingkat_prioritas'] ?? 'sedang',
                    'status_catatan' => $_POST['status_catatan'] ?? 'aktif',
                    'tindak_lanjut' => !empty($_POST['tindak_lanjut']) ? Security::sanitizeInput($_POST['tindak_lanjut']) : null,
                    'tanggal_tindak_lanjut' => $tanggalTindakLanjut,
                    'created_by' => $_SESSION['user_id'] ?? 1
                ];
                
                // Validate required fields
                if (empty($data['siswa_id'])) {
                    throw new Exception('ID siswa harus diisi');
                }
                if (empty($data['jenis_catatan'])) {
                    throw new Exception('Jenis catatan harus dipilih');
                }
                if (empty($data['judul_catatan'])) {
                    throw new Exception('Judul catatan harus diisi');
                }
                if (empty($data['isi_catatan'])) {
                    throw new Exception('Isi catatan harus diisi');
                }

                // Check if user can access this student
                Security::requireAccessToSiswa($data['siswa_id']);

                // Check if user can create this type of catatan
                if (!Security::canCreateCatatanType($data['jenis_catatan'])) {
                    throw new Exception('Anda tidak memiliki akses untuk membuat jenis catatan ini');
                }
                
                // Create catatan
                $result = $this->catatan->create($data);

                if ($result) {
                    $_SESSION['success'] = 'Catatan berhasil ditambahkan';

                    // Log the activity
                    Security::logSecurityEvent('catatan_created', [
                        'siswa_id' => $siswaId,
                        'jenis_catatan' => $jenisCatatan,
                        'catatan_id' => $result
                    ]);
                } else {
                    throw new Exception('Gagal menambahkan catatan ke database');
                }

            } catch (Exception $e) {
                error_log("Error in CatatanController::create(): " . $e->getMessage());
                $_SESSION['error'] = $e->getMessage();
            }
        } else {
            $_SESSION['error'] = 'Method tidak diizinkan';
        }

        // Redirect back to student detail
        $siswaId = $_POST['siswa_id'] ?? '';
        if ($siswaId) {
            header('Location: /siswa-app/public/siswa/detail/' . $siswaId);
        } else {
            header('Location: /siswa-app/public/siswa');
        }
        exit;
    }

    /**
     * Validate date format
     */
    private function validateDate($date, $format = 'Y-m-d') {
        $d = DateTime::createFromFormat($format, $date);
        return $d && $d->format($format) === $date;
    }
    
    /**
     * Get catatan detail (AJAX)
     */
    public function detail($id) {
        Security::requireAuth();
        
        header('Content-Type: application/json');
        
        try {
            $catatanDetail = $this->catatan->getById($id);
            
            if (!$catatanDetail) {
                throw new Exception('Catatan tidak ditemukan');
            }
            
            echo json_encode([
                'success' => true,
                'catatan' => $catatanDetail
            ]);
            
        } catch (Exception $e) {
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
        exit;
    }
    
    /**
     * Edit catatan
     */
    public function edit($id) {
        Security::requireAuth();
        
        $catatanDetail = $this->catatan->getById($id);
        if (!$catatanDetail) {
            $_SESSION['error'] = 'Catatan tidak ditemukan';
            header('Location: /siswa-app/public/siswa');
            exit;
        }
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            try {
                // Validate CSRF token
                if (!Security::validateCSRFToken($_POST['csrf_token'] ?? '')) {
                    throw new Exception('Token keamanan tidak valid');
                }
                
                // Get form data
                $data = [
                    'judul_catatan' => $_POST['judul_catatan'] ?? '',
                    'isi_catatan' => $_POST['isi_catatan'] ?? '',
                    'tanggal_catatan' => $_POST['tanggal_catatan'] ?? date('Y-m-d'),
                    'tingkat_prioritas' => $_POST['tingkat_prioritas'] ?? 'sedang',
                    'status_catatan' => $_POST['status_catatan'] ?? 'aktif',
                    'tindak_lanjut' => $_POST['tindak_lanjut'] ?? null,
                    'tanggal_tindak_lanjut' => $_POST['tanggal_tindak_lanjut'] ?? null,
                    'updated_by' => $_SESSION['user_id'] ?? 1
                ];
                
                // Validate required fields
                if (empty($data['judul_catatan'])) {
                    throw new Exception('Judul catatan harus diisi');
                }
                if (empty($data['isi_catatan'])) {
                    throw new Exception('Isi catatan harus diisi');
                }
                
                // Update catatan
                $result = $this->catatan->update($id, $data);
                
                if ($result) {
                    $_SESSION['success'] = 'Catatan berhasil diperbarui';
                    header('Location: /siswa-app/public/siswa/detail/' . $catatanDetail['siswa_id']);
                    exit;
                } else {
                    throw new Exception('Gagal memperbarui catatan');
                }
                
            } catch (Exception $e) {
                $_SESSION['error'] = $e->getMessage();
            }
        }
        
        // Get categories for dropdown
        $categories = $this->catatan->getCategoriesGrouped();
        
        $data = [
            'title' => 'Edit Catatan',
            'catatan' => $catatanDetail,
            'catatan_categories' => $categories,
            'csrf_token' => Security::generateCSRFToken()
        ];
        
        $this->view('catatan/edit', $data);
    }
    
    /**
     * Delete catatan (AJAX)
     */
    public function delete($id) {
        Security::requireAuth();
        
        header('Content-Type: application/json');
        
        try {
            // Get catatan first to check ownership/permission
            $catatanDetail = $this->catatan->getById($id);
            if (!$catatanDetail) {
                throw new Exception('Catatan tidak ditemukan');
            }
            
            // Check if user has permission to delete
            $currentUserId = $_SESSION['user_id'] ?? 0;
            if ($catatanDetail['created_by'] != $currentUserId && !Security::hasRole('admin')) {
                throw new Exception('Anda tidak memiliki izin untuk menghapus catatan ini');
            }
            
            $result = $this->catatan->delete($id);
            
            if ($result) {
                echo json_encode([
                    'success' => true,
                    'message' => 'Catatan berhasil dihapus'
                ]);
            } else {
                throw new Exception('Gagal menghapus catatan');
            }
            
        } catch (Exception $e) {
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
        exit;
    }
    
    /**
     * List all catatan for a student
     */
    public function index($siswaId = null) {
        Security::requireAuth();
        
        if (!$siswaId) {
            $_SESSION['error'] = 'ID siswa tidak valid';
            header('Location: /siswa-app/public/siswa');
            exit;
        }
        
        // Get student info
        require_once __DIR__ . '/../models/Siswa.php';
        $siswaModel = new Siswa();
        $siswa = $siswaModel->getById($siswaId);
        
        if (!$siswa) {
            $_SESSION['error'] = 'Data siswa tidak ditemukan';
            header('Location: /siswa-app/public/siswa');
            exit;
        }
        
        // Get catatan
        $catatanGrouped = $this->catatan->getGroupedBySiswaId($siswaId);
        $categories = $this->catatan->getCategoriesGrouped();
        $statistics = $this->catatan->getStatistics($siswaId);
        
        $data = [
            'title' => 'Catatan Siswa - ' . $siswa['nama_lengkap'],
            'siswa' => $siswa,
            'catatan_grouped' => $catatanGrouped,
            'catatan_categories' => $categories,
            'statistics' => $statistics,
            'csrf_token' => Security::generateCSRFToken()
        ];
        
        $this->view('catatan/index', $data);
    }
    
    private function view($path, $data = []) {
        extract($data);
        require_once __DIR__ . '/../views/layouts/header.php';
        require_once __DIR__ . '/../views/' . $path . '.php';
        require_once __DIR__ . '/../views/layouts/footer.php';
    }
    
    private function getById($id) {
        try {
            return $this->catatan->db->fetch("
                SELECT c.*, k.nama_kategori, k.warna_badge, k.icon_class, u.username as created_by_name
                FROM catatan_siswa c
                LEFT JOIN kategori_catatan k ON c.jenis_catatan = k.kode_kategori
                LEFT JOIN users u ON c.created_by = u.id
                WHERE c.id = ?
            ", [$id]);
        } catch (Exception $e) {
            return null;
        }
    }
}
?>
