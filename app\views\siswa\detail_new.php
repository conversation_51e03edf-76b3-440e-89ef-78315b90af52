<?php
// Check if siswa data exists
if (!isset($siswa) || empty($siswa)) {
    echo '<div class="alert alert-danger">Data siswa tidak ditemukan.</div>';
    return;
}

// Security helper function
if (!class_exists('Security') || !method_exists('Security', 'canEditSiswa')) {
    class SecurityHelper {
        public static function canEditSiswa($siswaId) {
            // Default: allow edit if user is logged in
            return isset($_SESSION['user_id']);
        }
    }

    if (!class_exists('Security')) {
        class Security {
            public static function canEditSiswa($siswaId) {
                return SecurityHelper::canEditSiswa($siswaId);
            }
        }
    }
}

// Load berkas model safely
try {
    require_once __DIR__ . '/../../models/Berkas.php';
    $berkas_model = new Berkas();
    $berkas = $berkas_model->getBySiswaId($siswa['id_siswa'] ?? $siswa['id'] ?? 0);
} catch (Exception $e) {
    $berkas = [];
}

// Load absensi data
try {
    require_once __DIR__ . '/../../models/Absensi.php';
    $absensi_model = new Absensi();
    $absensi_data = $absensi_model->getAbsensiByStudent($siswa['id_siswa'] ?? $siswa['id'] ?? 0);
    $absensi_summary = $absensi_model->getAbsensiSummary($siswa['id_siswa'] ?? $siswa['id'] ?? 0);
} catch (Exception $e) {
    $absensi_data = [];
    $absensi_summary = [
        'total_hadir' => 0,
        'total_sakit' => 0,
        'total_ijin' => 0,
        'total_alpha' => 0,
        'persentase_kehadiran' => 100
    ];
}
?>

<div class="container-fluid py-4">
    <!-- Main Layout -->
    <div class="row">
        <!-- Left Column - Student Profile -->
        <div class="col-lg-4 mb-4">
            <!-- Student Profile Card -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-body text-center p-4">
                    <!-- Profile Photo -->
                    <div class="position-relative mb-3">
                        <div class="profile-photo mx-auto mb-3">
                            <?php if (!empty($siswa['foto'])): ?>
                                <img src="/siswa-app/public/uploads/foto_siswa/<?= htmlspecialchars($siswa['foto']) ?>" 
                                     alt="Foto <?= htmlspecialchars($siswa['nama_lengkap']) ?>"
                                     class="rounded-circle img-fluid shadow">
                            <?php else: ?>
                                <div class="bg-light text-muted rounded-circle d-flex align-items-center justify-content-center shadow">
                                    <i class="bi bi-person-fill" style="font-size: 3rem;"></i>
                                </div>
                            <?php endif; ?>
                        </div>
                        <?php if (Security::canEditSiswa($siswa['id_siswa'])): ?>
                        <button class="btn btn-primary btn-sm position-absolute bottom-0 end-0 rounded-circle upload-photo-btn" 
                                style="width: 35px; height: 35px; right: 20px !important;"
                                data-bs-toggle="modal" data-bs-target="#uploadFotoModal">
                            <i class="bi bi-camera"></i>
                        </button>
                        <?php endif; ?>
                    </div>
                    
                    <!-- Student Name & Basic Info -->
                    <h4 class="fw-bold mb-2"><?= htmlspecialchars($siswa['nama_lengkap']) ?></h4>
                    <p class="text-muted mb-1">
                        <i class="bi bi-card-text me-1"></i>
                        NIS: <?= htmlspecialchars($siswa['nis']) ?>
                    </p>
                    <p class="text-muted mb-3">
                        <i class="bi bi-mortarboard me-1"></i>
                        Kelas: <?= htmlspecialchars($siswa['nama_kelas']) ?>
                    </p>
                    
                    <!-- Status Badge -->
                    <div class="mb-3">
                        <?php if ($siswa['status_siswa'] === 'aktif'): ?>
                            <span class="badge bg-success px-3 py-2">
                                <i class="bi bi-check-circle me-1"></i>
                                Aktif
                            </span>
                        <?php else: ?>
                            <span class="badge bg-secondary px-3 py-2">
                                <i class="bi bi-pause-circle me-1"></i>
                                <?= ucfirst($siswa['status_siswa']) ?>
                            </span>
                        <?php endif; ?>
                    </div>
                    
                    <!-- Action Buttons -->
                    <div class="d-grid gap-2">
                        <?php if (Security::canEditSiswa($siswa['id_siswa'])): ?>
                        <a href="/siswa-app/public/siswa/edit/<?= $siswa['id_siswa'] ?>" class="btn btn-warning">
                            <i class="bi bi-pencil me-1"></i>
                            Edit Data Siswa
                        </a>
                        <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#uploadBerkasModal">
                            <i class="bi bi-upload me-1"></i>
                            Upload Berkas
                        </button>
                        <button class="btn btn-info text-white" data-bs-toggle="modal" data-bs-target="#addCatatanModal" data-siswa-id="<?= $siswa['id_siswa'] ?>">
                            <i class="bi bi-plus-circle me-1"></i>
                            Tambah Catatan
                        </button>
                        <?php endif; ?>
                        <a href="/siswa-app/public/siswa" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-left me-1"></i>
                            Kembali ke Daftar
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Data Orang Tua Card -->
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h6 class="mb-0 fw-bold">
                        <i class="bi bi-people me-2"></i>
                        Data Orang Tua
                    </h6>
                </div>
                <div class="card-body p-3">
                    <!-- Father Information -->
                    <div class="parent-info mb-3">
                        <div class="d-flex align-items-center mb-2">
                            <div class="bg-primary bg-opacity-10 rounded-circle p-2 me-2" style="width: 35px; height: 35px;">
                                <i class="bi bi-person-fill text-primary" style="font-size: 1rem;"></i>
                            </div>
                            <h6 class="mb-0 fw-bold text-primary">Ayah</h6>
                        </div>
                        <div class="ps-3">
                            <div class="mb-1">
                                <small class="text-muted d-block">Nama Lengkap</small>
                                <strong><?= htmlspecialchars($siswa['nama_ayah'] ?? 'Tidak diketahui') ?></strong>
                            </div>
                            <div class="mb-1">
                                <small class="text-muted d-block">Pekerjaan</small>
                                <span class="badge bg-primary bg-opacity-10 text-primary">
                                    <?= htmlspecialchars($siswa['pekerjaan_ayah'] ?? 'Tidak diketahui') ?>
                                </span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Mother Information -->
                    <div class="parent-info">
                        <div class="d-flex align-items-center mb-2">
                            <div class="bg-danger bg-opacity-10 rounded-circle p-2 me-2" style="width: 35px; height: 35px;">
                                <i class="bi bi-person-fill text-danger" style="font-size: 1rem;"></i>
                            </div>
                            <h6 class="mb-0 fw-bold text-danger">Ibu</h6>
                        </div>
                        <div class="ps-3">
                            <div class="mb-1">
                                <small class="text-muted d-block">Nama Lengkap</small>
                                <strong><?= htmlspecialchars($siswa['nama_ibu'] ?? 'Tidak diketahui') ?></strong>
                            </div>
                            <div class="mb-1">
                                <small class="text-muted d-block">Pekerjaan</small>
                                <span class="badge bg-danger bg-opacity-10 text-danger">
                                    <?= htmlspecialchars($siswa['pekerjaan_ibu'] ?? 'Tidak diketahui') ?>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Right Column - Main Content -->
        <div class="col-lg-8">
            <!-- Navigation Tabs -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white border-bottom-0">
                    <ul class="nav nav-tabs card-header-tabs" id="studentTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="personal-tab" data-bs-toggle="tab" data-bs-target="#personal" type="button" role="tab">
                                <i class="bi bi-person me-1"></i>
                                Informasi Personal
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="absensi-tab" data-bs-toggle="tab" data-bs-target="#absensi" type="button" role="tab">
                                <i class="bi bi-calendar-check me-1"></i>
                                Data Absensi
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="akademik-tab" data-bs-toggle="tab" data-bs-target="#akademik" type="button" role="tab">
                                <i class="bi bi-book me-1"></i>
                                Informasi Akademik
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="catatan-tab" data-bs-toggle="tab" data-bs-target="#catatan" type="button" role="tab">
                                <i class="bi bi-journal-text me-1"></i>
                                Catatan Siswa
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="berkas-tab" data-bs-toggle="tab" data-bs-target="#berkas" type="button" role="tab">
                                <i class="bi bi-folder me-1"></i>
                                Berkas Siswa
                            </button>
                        </li>
                    </ul>
                </div>
                <div class="card-body">
                    <div class="tab-content" id="studentTabsContent">
                        <!-- Personal Information Tab -->
                        <div class="tab-pane fade show active" id="personal" role="tabpanel">
                            <div class="row g-4">
                                <!-- Birth Information -->
                                <div class="col-md-6">
                                    <div class="info-group">
                                        <h6 class="text-muted mb-3 fw-bold">
                                            <i class="bi bi-calendar-heart text-danger me-2"></i>
                                            Data Kelahiran
                                        </h6>
                                        <div class="info-item mb-3">
                                            <div class="d-flex align-items-center">
                                                <div class="bg-danger bg-opacity-10 rounded-circle p-2 me-3">
                                                    <i class="bi bi-geo-alt text-danger"></i>
                                                </div>
                                                <div>
                                                    <small class="text-muted d-block">Tempat Lahir</small>
                                                    <strong><?= htmlspecialchars($siswa['tempat_lahir'] ?? 'Tidak diketahui') ?></strong>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="info-item mb-3">
                                            <div class="d-flex align-items-center">
                                                <div class="bg-danger bg-opacity-10 rounded-circle p-2 me-3">
                                                    <i class="bi bi-calendar-date text-danger"></i>
                                                </div>
                                                <div>
                                                    <small class="text-muted d-block">Tanggal Lahir</small>
                                                    <strong>
                                                        <?php
                                                        if (!empty($siswa['tanggal_lahir'])) {
                                                            $birthDate = new DateTime($siswa['tanggal_lahir']);
                                                            $today = new DateTime();
                                                            $age = $today->diff($birthDate)->y;
                                                            echo $birthDate->format('d F Y') . " <small class='text-muted'>($age tahun)</small>";
                                                        } else {
                                                            echo 'Tidak diketahui';
                                                        }
                                                        ?>
                                                    </strong>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Contact Information -->
                                <div class="col-md-6">
                                    <div class="info-group">
                                        <h6 class="text-muted mb-3 fw-bold">
                                            <i class="bi bi-telephone text-success me-2"></i>
                                            Kontak
                                        </h6>
                                        <div class="info-item mb-3">
                                            <div class="d-flex align-items-center">
                                                <div class="bg-success bg-opacity-10 rounded-circle p-2 me-3">
                                                    <i class="bi bi-phone text-success"></i>
                                                </div>
                                                <div>
                                                    <small class="text-muted d-block">No. Telepon</small>
                                                    <?php if (!empty($siswa['no_telepon'])): ?>
                                                        <strong><?= htmlspecialchars($siswa['no_telepon']) ?></strong>
                                                    <?php else: ?>
                                                        <span class="text-muted">Tidak ada</span>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="info-item mb-3">
                                            <div class="d-flex align-items-center">
                                                <div class="bg-success bg-opacity-10 rounded-circle p-2 me-3">
                                                    <i class="bi bi-envelope text-success"></i>
                                                </div>
                                                <div>
                                                    <small class="text-muted d-block">Email</small>
                                                    <?php if (!empty($siswa['email'])): ?>
                                                        <strong><?= htmlspecialchars($siswa['email']) ?></strong>
                                                    <?php else: ?>
                                                        <span class="text-muted">Tidak ada</span>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Address Section -->
                            <?php if (!empty($siswa['alamat'])): ?>
                            <div class="row mt-4">
                                <div class="col-12">
                                    <div class="info-group">
                                        <h6 class="text-muted mb-3 fw-bold">
                                            <i class="bi bi-house text-info me-2"></i>
                                            Alamat Lengkap
                                        </h6>
                                        <div class="bg-light rounded p-3">
                                            <div class="d-flex">
                                                <div class="bg-info bg-opacity-10 rounded-circle p-2 me-3 flex-shrink-0">
                                                    <i class="bi bi-geo-alt-fill text-info"></i>
                                                </div>
                                                <div>
                                                    <p class="mb-0"><?= nl2br(htmlspecialchars($siswa['alamat'])) ?></p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>

                        <!-- Data Absensi Tab -->
                        <div class="tab-pane fade" id="absensi" role="tabpanel">
                            <!-- Absensi Summary Cards -->
                            <div class="row g-3 mb-4">
                                <div class="col-md-3">
                                    <div class="card bg-success bg-opacity-10 border-success border-opacity-25">
                                        <div class="card-body text-center p-3">
                                            <div class="d-flex align-items-center justify-content-center mb-2">
                                                <div class="bg-success rounded-circle p-2 me-2">
                                                    <i class="bi bi-check-circle text-white"></i>
                                                </div>
                                                <h4 class="mb-0 fw-bold text-success"><?= number_format($absensi_summary['persentase_kehadiran'], 1) ?>%</h4>
                                            </div>
                                            <small class="text-muted">Kehadiran</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-danger bg-opacity-10 border-danger border-opacity-25">
                                        <div class="card-body text-center p-3">
                                            <div class="d-flex align-items-center justify-content-center mb-2">
                                                <div class="bg-danger rounded-circle p-2 me-2">
                                                    <i class="bi bi-thermometer-half text-white"></i>
                                                </div>
                                                <h4 class="mb-0 fw-bold text-danger"><?= $absensi_summary['total_sakit'] ?></h4>
                                            </div>
                                            <small class="text-muted">Sakit</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-warning bg-opacity-10 border-warning border-opacity-25">
                                        <div class="card-body text-center p-3">
                                            <div class="d-flex align-items-center justify-content-center mb-2">
                                                <div class="bg-warning rounded-circle p-2 me-2">
                                                    <i class="bi bi-file-text text-white"></i>
                                                </div>
                                                <h4 class="mb-0 fw-bold text-warning"><?= $absensi_summary['total_ijin'] ?></h4>
                                            </div>
                                            <small class="text-muted">Ijin</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-secondary bg-opacity-10 border-secondary border-opacity-25">
                                        <div class="card-body text-center p-3">
                                            <div class="d-flex align-items-center justify-content-center mb-2">
                                                <div class="bg-secondary rounded-circle p-2 me-2">
                                                    <i class="bi bi-x-circle text-white"></i>
                                                </div>
                                                <h4 class="mb-0 fw-bold text-secondary"><?= $absensi_summary['total_alpha'] ?></h4>
                                            </div>
                                            <small class="text-muted">Alpha</small>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Add Absensi Button -->
                            <?php if (Security::canEditSiswa($siswa['id_siswa'])): ?>
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6 class="mb-0 fw-bold">Riwayat Absensi</h6>
                                <a href="/siswa-app/public/absensi/add/<?= $siswa['id_siswa'] ?>" class="btn btn-primary btn-sm">
                                    <i class="bi bi-plus-circle me-1"></i>
                                    Tambah Absensi
                                </a>
                            </div>
                            <?php endif; ?>

                            <!-- Absensi Table -->
                            <?php if (!empty($absensi_data)): ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>Tanggal</th>
                                            <th>Jenis</th>
                                            <th>Keterangan</th>
                                            <th>Surat</th>
                                            <th>Dicatat Oleh</th>
                                            <?php if (Security::canEditSiswa($siswa['id_siswa'])): ?>
                                            <th>Aksi</th>
                                            <?php endif; ?>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($absensi_data as $abs): ?>
                                        <tr>
                                            <td>
                                                <div class="d-flex flex-column">
                                                    <strong><?= date('d/m/Y', strtotime($abs['tanggal'])) ?></strong>
                                                    <small class="text-muted"><?= date('l', strtotime($abs['tanggal'])) ?></small>
                                                </div>
                                            </td>
                                            <td>
                                                <?php
                                                $jenis_config = [
                                                    'sakit' => ['color' => 'danger', 'icon' => 'bi-thermometer-half', 'label' => 'Sakit'],
                                                    'ijin' => ['color' => 'warning', 'icon' => 'bi-file-text', 'label' => 'Ijin'],
                                                    'alpha' => ['color' => 'secondary', 'icon' => 'bi-x-circle', 'label' => 'Alpha']
                                                ];
                                                $config = $jenis_config[$abs['jenis_ketidakhadiran']] ?? $jenis_config['alpha'];
                                                ?>
                                                <span class="badge bg-<?= $config['color'] ?> d-flex align-items-center" style="width: fit-content;">
                                                    <i class="<?= $config['icon'] ?> me-1"></i>
                                                    <?= $config['label'] ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php if (!empty($abs['keterangan'])): ?>
                                                    <span class="text-truncate" style="max-width: 200px;" title="<?= htmlspecialchars($abs['keterangan']) ?>">
                                                        <?= htmlspecialchars(substr($abs['keterangan'], 0, 50)) ?><?= strlen($abs['keterangan']) > 50 ? '...' : '' ?>
                                                    </span>
                                                <?php else: ?>
                                                    <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if (!empty($abs['surat_keterangan'])): ?>
                                                    <a href="/siswa-app/public/uploads/surat_keterangan/<?= htmlspecialchars($abs['surat_keterangan']) ?>"
                                                       target="_blank" class="btn btn-outline-primary btn-sm">
                                                        <i class="bi bi-file-earmark-text me-1"></i>
                                                        Lihat Surat
                                                    </a>
                                                <?php else: ?>
                                                    <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <small class="text-muted">
                                                    <?= htmlspecialchars($abs['created_by_name'] ?? 'System') ?><br>
                                                    <?= date('d/m/Y H:i', strtotime($abs['created_at'])) ?>
                                                </small>
                                            </td>
                                            <?php if (Security::canEditSiswa($siswa['id_siswa'])): ?>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="/siswa-app/public/absensi/edit/<?= $abs['id'] ?>"
                                                       class="btn btn-outline-warning btn-sm" title="Edit">
                                                        <i class="bi bi-pencil"></i>
                                                    </a>
                                                    <button class="btn btn-outline-danger btn-sm"
                                                            data-bs-toggle="modal"
                                                            data-bs-target="#deleteAbsensiModal"
                                                            data-absensi-id="<?= $abs['id'] ?>"
                                                            data-tanggal="<?= date('d/m/Y', strtotime($abs['tanggal'])) ?>"
                                                            title="Hapus">
                                                        <i class="bi bi-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                            <?php endif; ?>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            <?php else: ?>
                            <div class="text-center py-5">
                                <div class="mb-3">
                                    <i class="bi bi-calendar-x text-muted" style="font-size: 3rem;"></i>
                                </div>
                                <h6 class="text-muted">Belum ada catatan absensi</h6>
                                <p class="text-muted mb-3">Siswa ini belum memiliki catatan ketidakhadiran</p>
                                <?php if (Security::canEditSiswa($siswa['id_siswa'])): ?>
                                <a href="/siswa-app/public/absensi/add/<?= $siswa['id_siswa'] ?>" class="btn btn-primary">
                                    <i class="bi bi-plus-circle me-1"></i>
                                    Tambah Absensi Pertama
                                </a>
                                <?php endif; ?>
                            </div>
                            <?php endif; ?>
                        </div>

                        <!-- Informasi Akademik Tab -->
                        <div class="tab-pane fade" id="akademik" role="tabpanel">
                            <div class="row g-4">
                                <div class="col-md-6">
                                    <div class="card bg-warning bg-opacity-10 border-warning border-opacity-25">
                                        <div class="card-body text-center">
                                            <div class="bg-warning bg-opacity-10 rounded-circle p-3 mx-auto mb-3" style="width: 60px; height: 60px;">
                                                <i class="bi bi-calendar-plus text-warning fs-4"></i>
                                            </div>
                                            <h6 class="fw-bold">Tahun Masuk</h6>
                                            <h4 class="text-warning fw-bold"><?= htmlspecialchars($siswa['tahun_masuk'] ?? 'N/A') ?></h4>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card bg-info bg-opacity-10 border-info border-opacity-25">
                                        <div class="card-body text-center">
                                            <div class="bg-info bg-opacity-10 rounded-circle p-3 mx-auto mb-3" style="width: 60px; height: 60px;">
                                                <i class="bi bi-calendar-check text-info fs-4"></i>
                                            </div>
                                            <h6 class="fw-bold">Terdaftar Sejak</h6>
                                            <h4 class="text-info fw-bold">
                                                <?php
                                                if (!empty($siswa['created_at'])) {
                                                    echo date('d M Y', strtotime($siswa['created_at']));
                                                } else {
                                                    echo 'N/A';
                                                }
                                                ?>
                                            </h4>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Catatan Siswa Tab -->
                        <div class="tab-pane fade" id="catatan" role="tabpanel">
                            <?php if (!empty($catatan_grouped)): ?>
                                <!-- Catatan Summary Cards -->
                                <div class="row g-3 mb-4">
                                    <?php
                                    $totalCatatan = 0;
                                    foreach ($catatan_grouped as $jenis => $catatanList) {
                                        $totalCatatan += count($catatanList);
                                    }
                                    ?>
                                    <div class="col-md-3">
                                        <div class="card bg-info bg-opacity-10 border-info border-opacity-25">
                                            <div class="card-body text-center p-3">
                                                <div class="bg-info rounded-circle p-2 mx-auto mb-2" style="width: 40px; height: 40px;">
                                                    <i class="bi bi-journal-text text-white"></i>
                                                </div>
                                                <h4 class="mb-0 fw-bold text-info"><?= $totalCatatan ?></h4>
                                                <small class="text-muted">Total Catatan</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card bg-success bg-opacity-10 border-success border-opacity-25">
                                            <div class="card-body text-center p-3">
                                                <div class="bg-success rounded-circle p-2 mx-auto mb-2" style="width: 40px; height: 40px;">
                                                    <i class="bi bi-award text-white"></i>
                                                </div>
                                                <h4 class="mb-0 fw-bold text-success">
                                                    <?= count($catatan_grouped['prestasi'] ?? []) ?>
                                                </h4>
                                                <small class="text-muted">Prestasi</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card bg-warning bg-opacity-10 border-warning border-opacity-25">
                                            <div class="card-body text-center p-3">
                                                <div class="bg-warning rounded-circle p-2 mx-auto mb-2" style="width: 40px; height: 40px;">
                                                    <i class="bi bi-exclamation-triangle text-white"></i>
                                                </div>
                                                <h4 class="mb-0 fw-bold text-warning">
                                                    <?= count($catatan_grouped['pelanggaran'] ?? []) ?>
                                                </h4>
                                                <small class="text-muted">Pelanggaran</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card bg-primary bg-opacity-10 border-primary border-opacity-25">
                                            <div class="card-body text-center p-3">
                                                <div class="bg-primary rounded-circle p-2 mx-auto mb-2" style="width: 40px; height: 40px;">
                                                    <i class="bi bi-people text-white"></i>
                                                </div>
                                                <h4 class="mb-0 fw-bold text-primary">
                                                    <?= count($catatan_grouped['wali_kelas'] ?? []) + count($catatan_grouped['pamong'] ?? []) + count($catatan_grouped['bk'] ?? []) ?>
                                                </h4>
                                                <small class="text-muted">Bimbingan</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Add Catatan Button -->
                                <?php if (Security::canEditSiswa($siswa['id_siswa'])): ?>
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h6 class="mb-0 fw-bold">Riwayat Catatan</h6>
                                    <button class="btn btn-info text-white btn-sm" data-bs-toggle="modal" data-bs-target="#addCatatanModal">
                                        <i class="bi bi-plus-circle me-1"></i>
                                        Tambah Catatan
                                    </button>
                                </div>
                                <?php endif; ?>

                                <!-- Catatan by Category -->
                                <div class="accordion" id="catatanAccordion">
                                    <?php foreach ($catatan_grouped as $jenis => $catatanList): ?>
                                        <?php if (!empty($catatanList)): ?>
                                        <div class="accordion-item border-0 shadow-sm mb-3">
                                            <h2 class="accordion-header" id="heading<?= ucfirst($jenis) ?>">
                                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse<?= ucfirst($jenis) ?>" aria-expanded="false">
                                                    <?php
                                                    $jenisConfig = [
                                                        'wali_kelas' => ['icon' => 'bi-person-badge', 'color' => 'primary', 'label' => 'Catatan Wali Kelas'],
                                                        'pamong' => ['icon' => 'bi-people', 'color' => 'info', 'label' => 'Catatan Pamong'],
                                                        'bk' => ['icon' => 'bi-heart', 'color' => 'success', 'label' => 'Catatan BK'],
                                                        'akademik' => ['icon' => 'bi-book', 'color' => 'warning', 'label' => 'Catatan Akademik'],
                                                        'prestasi' => ['icon' => 'bi-award', 'color' => 'success', 'label' => 'Prestasi'],
                                                        'pelanggaran' => ['icon' => 'bi-exclamation-triangle', 'color' => 'danger', 'label' => 'Pelanggaran']
                                                    ];
                                                    $config = $jenisConfig[$jenis] ?? ['icon' => 'bi-journal', 'color' => 'secondary', 'label' => ucfirst($jenis)];
                                                    ?>
                                                    <div class="d-flex align-items-center">
                                                        <div class="bg-<?= $config['color'] ?> bg-opacity-10 rounded-circle p-2 me-3">
                                                            <i class="<?= $config['icon'] ?> text-<?= $config['color'] ?>"></i>
                                                        </div>
                                                        <div>
                                                            <strong><?= $config['label'] ?></strong>
                                                            <span class="badge bg-<?= $config['color'] ?> ms-2"><?= count($catatanList) ?></span>
                                                        </div>
                                                    </div>
                                                </button>
                                            </h2>
                                            <div id="collapse<?= ucfirst($jenis) ?>" class="accordion-collapse collapse" data-bs-parent="#catatanAccordion">
                                                <div class="accordion-body">
                                                    <div class="row g-3">
                                                        <?php foreach ($catatanList as $catatan): ?>
                                                        <div class="col-12">
                                                            <div class="card border-start border-<?= $config['color'] ?> border-3">
                                                                <div class="card-body p-3">
                                                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                                                        <h6 class="mb-1 fw-bold"><?= htmlspecialchars($catatan['judul'] ?? 'Catatan ' . $config['label']) ?></h6>
                                                                        <small class="text-muted">
                                                                            <i class="bi bi-calendar me-1"></i>
                                                                            <?= date('d/m/Y', strtotime($catatan['tanggal'] ?? $catatan['created_at'])) ?>
                                                                        </small>
                                                                    </div>
                                                                    <p class="mb-2 text-muted"><?= nl2br(htmlspecialchars($catatan['catatan'] ?? $catatan['isi_catatan'] ?? '')) ?></p>

                                                                    <?php if (!empty($catatan['tindak_lanjut'])): ?>
                                                                    <div class="bg-light rounded p-2 mb-2">
                                                                        <small class="fw-bold text-muted d-block">Tindak Lanjut:</small>
                                                                        <small class="text-muted"><?= nl2br(htmlspecialchars($catatan['tindak_lanjut'])) ?></small>
                                                                    </div>
                                                                    <?php endif; ?>

                                                                    <div class="d-flex justify-content-between align-items-center">
                                                                        <small class="text-muted">
                                                                            <i class="bi bi-person me-1"></i>
                                                                            <?= htmlspecialchars($catatan['created_by_name'] ?? 'System') ?>
                                                                        </small>
                                                                        <?php if (Security::canEditSiswa($siswa['id_siswa'])): ?>
                                                                        <div class="btn-group btn-group-sm">
                                                                            <button class="btn btn-outline-warning btn-sm"
                                                                                    onclick="editCatatan(<?= $catatan['id'] ?>)" title="Edit">
                                                                                <i class="bi bi-pencil"></i>
                                                                            </button>
                                                                            <button class="btn btn-outline-danger btn-sm"
                                                                                    onclick="deleteCatatan(<?= $catatan['id'] ?>)" title="Hapus">
                                                                                <i class="bi bi-trash"></i>
                                                                            </button>
                                                                        </div>
                                                                        <?php endif; ?>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <?php endforeach; ?>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <?php endif; ?>
                                    <?php endforeach; ?>
                                </div>
                            <?php else: ?>
                            <div class="text-center py-5">
                                <div class="mb-3">
                                    <i class="bi bi-journal-x text-muted" style="font-size: 3rem;"></i>
                                </div>
                                <h6 class="text-muted">Belum ada catatan</h6>
                                <p class="text-muted mb-3">Siswa ini belum memiliki catatan apapun</p>
                                <?php if (Security::canEditSiswa($siswa['id_siswa'])): ?>
                                <button class="btn btn-info text-white" data-bs-toggle="modal" data-bs-target="#addCatatanModal">
                                    <i class="bi bi-plus-circle me-1"></i>
                                    Tambah Catatan Pertama
                                </button>
                                <?php endif; ?>
                            </div>
                            <?php endif; ?>
                        </div>

                        <!-- Berkas Siswa Tab -->
                        <div class="tab-pane fade" id="berkas" role="tabpanel">
                            <?php if (!empty($berkas)): ?>
                                <!-- File Statistics -->
                                <div class="row g-3 mb-4">
                                    <div class="col-md-4">
                                        <div class="card bg-primary bg-opacity-10 border-primary border-opacity-25">
                                            <div class="card-body text-center p-3">
                                                <div class="bg-primary rounded-circle p-2 mx-auto mb-2" style="width: 40px; height: 40px;">
                                                    <i class="bi bi-files text-white"></i>
                                                </div>
                                                <h4 class="mb-0 fw-bold text-primary"><?= count($berkas) ?></h4>
                                                <small class="text-muted">Total Berkas</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="card bg-success bg-opacity-10 border-success border-opacity-25">
                                            <div class="card-body text-center p-3">
                                                <div class="bg-success rounded-circle p-2 mx-auto mb-2" style="width: 40px; height: 40px;">
                                                    <i class="bi bi-folder text-white"></i>
                                                </div>
                                                <h4 class="mb-0 fw-bold text-success">
                                                    <?php
                                                    $categories = array_unique(array_column($berkas, 'jenis_berkas'));
                                                    echo count($categories);
                                                    ?>
                                                </h4>
                                                <small class="text-muted">Kategori</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="card bg-info bg-opacity-10 border-info border-opacity-25">
                                            <div class="card-body text-center p-3">
                                                <div class="bg-info rounded-circle p-2 mx-auto mb-2" style="width: 40px; height: 40px;">
                                                    <i class="bi bi-clock text-white"></i>
                                                </div>
                                                <h6 class="mb-0 fw-bold text-info">
                                                    <?php
                                                    if (!empty($berkas)) {
                                                        $latest = max(array_column($berkas, 'created_at'));
                                                        echo date('d/m/Y', strtotime($latest));
                                                    } else {
                                                        echo '-';
                                                    }
                                                    ?>
                                                </h6>
                                                <small class="text-muted">Terakhir Upload</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Upload Button -->
                                <?php if (Security::canEditSiswa($siswa['id_siswa'])): ?>
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h6 class="mb-0 fw-bold">Daftar Berkas</h6>
                                    <button class="btn btn-success btn-sm" data-bs-toggle="modal" data-bs-target="#uploadBerkasModal">
                                        <i class="bi bi-upload me-1"></i>
                                        Upload Berkas
                                    </button>
                                </div>
                                <?php endif; ?>

                                <!-- Files List -->
                                <div class="row g-3">
                                    <?php foreach ($berkas as $file): ?>
                                    <div class="col-md-6">
                                        <div class="card border-0 shadow-sm">
                                            <div class="card-body p-3">
                                                <div class="d-flex align-items-start">
                                                    <div class="me-3">
                                                        <?php
                                                        $extension = strtolower(pathinfo($file['nama_file'], PATHINFO_EXTENSION));
                                                        $iconClass = 'bi-file-earmark';
                                                        $iconColor = 'text-secondary';

                                                        if (in_array($extension, ['jpg', 'jpeg', 'png', 'gif'])) {
                                                            $iconClass = 'bi-file-earmark-image';
                                                            $iconColor = 'text-success';
                                                        } elseif ($extension === 'pdf') {
                                                            $iconClass = 'bi-file-earmark-pdf';
                                                            $iconColor = 'text-danger';
                                                        } elseif (in_array($extension, ['doc', 'docx'])) {
                                                            $iconClass = 'bi-file-earmark-word';
                                                            $iconColor = 'text-primary';
                                                        }
                                                        ?>
                                                        <div class="bg-light rounded p-2">
                                                            <i class="<?= $iconClass ?> <?= $iconColor ?> fs-4"></i>
                                                        </div>
                                                    </div>
                                                    <div class="flex-grow-1">
                                                        <h6 class="mb-1 fw-bold"><?= htmlspecialchars($file['jenis_berkas']) ?></h6>
                                                        <p class="mb-1 text-muted small"><?= htmlspecialchars($file['nama_file']) ?></p>
                                                        <small class="text-muted">
                                                            <i class="bi bi-calendar me-1"></i>
                                                            <?= date('d/m/Y H:i', strtotime($file['created_at'])) ?>
                                                        </small>
                                                    </div>
                                                    <div class="ms-2">
                                                        <div class="btn-group-vertical btn-group-sm">
                                                            <a href="/siswa-app/public/uploads/berkas/<?= htmlspecialchars($file['nama_file']) ?>"
                                                               target="_blank" class="btn btn-outline-primary btn-sm" title="Lihat">
                                                                <i class="bi bi-eye"></i>
                                                            </a>
                                                            <?php if (Security::canEditSiswa($siswa['id_siswa'])): ?>
                                                            <button class="btn btn-outline-danger btn-sm"
                                                                    onclick="deleteBerkas(<?= $file['id'] ?>)" title="Hapus">
                                                                <i class="bi bi-trash"></i>
                                                            </button>
                                                            <?php endif; ?>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php else: ?>
                            <div class="text-center py-5">
                                <div class="mb-3">
                                    <i class="bi bi-folder-x text-muted" style="font-size: 3rem;"></i>
                                </div>
                                <h6 class="text-muted">Belum ada berkas</h6>
                                <p class="text-muted mb-3">Siswa ini belum memiliki berkas yang diupload</p>
                                <?php if (Security::canEditSiswa($siswa['id_siswa'])): ?>
                                <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#uploadBerkasModal">
                                    <i class="bi bi-upload me-1"></i>
                                    Upload Berkas Pertama
                                </button>
                                <?php endif; ?>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Custom Styles -->
<style>
/* Profile Photo Styling */
.profile-photo {
    width: 150px;
    height: 150px;
    position: relative;
}

.profile-photo img,
.profile-photo > div {
    width: 150px;
    height: 150px;
    object-fit: cover;
}

/* Card Enhancements */
.card {
    border-radius: 12px;
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1) !important;
}

/* Tab Styling */
.nav-tabs .nav-link {
    border: none;
    border-radius: 8px 8px 0 0;
    color: #6c757d;
    font-weight: 500;
    padding: 12px 20px;
    margin-right: 4px;
    transition: all 0.3s ease;
}

.nav-tabs .nav-link:hover {
    background-color: #f8f9fa;
    color: #495057;
}

.nav-tabs .nav-link.active {
    background-color: #007bff;
    color: white;
    border-color: #007bff;
}

/* Button Enhancements */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

/* Badge Styling */
.badge {
    font-size: 0.75rem;
    font-weight: 500;
    padding: 0.5rem 0.75rem;
}

/* Table Enhancements */
.table-hover tbody tr:hover {
    background-color: rgba(0,123,255,0.05);
}

/* Info Group Styling */
.info-group {
    margin-bottom: 2rem;
}

.info-item {
    padding: 0.75rem 0;
    border-bottom: 1px solid #f1f3f4;
}

.info-item:last-child {
    border-bottom: none;
}

/* Parent Info Styling */
.parent-info {
    padding: 1rem;
    border-radius: 8px;
    background-color: #f8f9fa;
    margin-bottom: 1rem;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .profile-photo {
        width: 120px;
        height: 120px;
    }

    .profile-photo img,
    .profile-photo > div {
        width: 120px;
        height: 120px;
    }

    .nav-tabs .nav-link {
        padding: 8px 12px;
        font-size: 0.875rem;
    }
}

/* Loading Animation */
.btn.loading {
    pointer-events: none;
    opacity: 0.7;
}

.btn.loading i {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
</style>

<!-- Upload Foto Modal -->
<div class="modal fade" id="uploadFotoModal" tabindex="-1" aria-labelledby="uploadFotoModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="uploadFotoModalLabel">
                    <i class="bi bi-camera me-2"></i>
                    Upload Foto Siswa
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="/siswa-app/public/siswa/upload-foto/<?= $siswa['id_siswa'] ?>" method="POST" enctype="multipart/form-data">
                <div class="modal-body">
                    <input type="hidden" name="csrf_token" value="<?= $csrf_token ?>">

                    <div class="mb-3">
                        <label for="foto" class="form-label">Pilih Foto</label>
                        <input type="file" class="form-control" id="foto" name="foto" accept="image/*" required>
                        <div class="form-text">
                            <i class="bi bi-info-circle me-1"></i>
                            Format: JPG, JPEG, PNG. Maksimal 2MB.
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Preview</label>
                        <div id="fotoPreview" class="text-center">
                            <div class="bg-light rounded p-4">
                                <i class="bi bi-image text-muted" style="font-size: 3rem;"></i>
                                <p class="text-muted mb-0 mt-2">Preview foto akan muncul di sini</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-upload me-1"></i>
                        Upload Foto
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Upload Berkas Modal -->
<div class="modal fade" id="uploadBerkasModal" tabindex="-1" aria-labelledby="uploadBerkasModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="uploadBerkasModalLabel">
                    <i class="bi bi-upload me-2"></i>
                    Upload Berkas Siswa
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="/siswa-app/public/berkas/upload/<?= $siswa['id_siswa'] ?>" method="POST" enctype="multipart/form-data">
                <div class="modal-body">
                    <input type="hidden" name="csrf_token" value="<?= $csrf_token ?>">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="jenis_berkas" class="form-label">Jenis Berkas</label>
                                <select class="form-select" id="jenis_berkas" name="jenis_berkas" required>
                                    <option value="">Pilih Jenis Berkas</option>
                                    <option value="Akta Kelahiran">Akta Kelahiran</option>
                                    <option value="Kartu Keluarga">Kartu Keluarga</option>
                                    <option value="Ijazah">Ijazah</option>
                                    <option value="Rapor">Rapor</option>
                                    <option value="Surat Keterangan">Surat Keterangan</option>
                                    <option value="Foto">Foto</option>
                                    <option value="Lainnya">Lainnya</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="berkas_file" class="form-label">File Berkas</label>
                                <input type="file" class="form-control" id="berkas_file" name="berkas_file" required>
                                <div class="form-text">
                                    <i class="bi bi-info-circle me-1"></i>
                                    Format: PDF, JPG, PNG, DOC, DOCX. Maksimal 5MB.
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="keterangan_berkas" class="form-label">Keterangan (Opsional)</label>
                        <textarea class="form-control" id="keterangan_berkas" name="keterangan" rows="3" placeholder="Tambahkan keterangan untuk berkas ini..."></textarea>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Preview File</label>
                        <div id="berkasPreview" class="text-center">
                            <div class="bg-light rounded p-4">
                                <i class="bi bi-file-earmark text-muted" style="font-size: 3rem;"></i>
                                <p class="text-muted mb-0 mt-2">Preview file akan muncul di sini</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-success">
                        <i class="bi bi-upload me-1"></i>
                        Upload Berkas
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Tambah Catatan Modal -->
<div class="modal fade" id="addCatatanModal" tabindex="-1" aria-labelledby="addCatatanModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addCatatanModalLabel">
                    <i class="bi bi-journal-plus me-2"></i>
                    Tambah Catatan Siswa
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="/siswa-app/public/catatan/add" method="POST">
                <div class="modal-body">
                    <input type="hidden" name="csrf_token" value="<?= $csrf_token ?>">
                    <input type="hidden" name="siswa_id" value="<?= $siswa['id_siswa'] ?>">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="jenis_catatan" class="form-label">Jenis Catatan</label>
                                <select class="form-select" id="jenis_catatan" name="jenis_catatan" required>
                                    <option value="">Pilih Jenis Catatan</option>
                                    <option value="wali_kelas">Catatan Wali Kelas</option>
                                    <option value="pamong">Catatan Pamong</option>
                                    <option value="bk">Catatan BK</option>
                                    <option value="akademik">Catatan Akademik</option>
                                    <option value="prestasi">Prestasi</option>
                                    <option value="pelanggaran">Pelanggaran</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="tanggal_catatan" class="form-label">Tanggal</label>
                                <input type="date" class="form-control" id="tanggal_catatan" name="tanggal" value="<?= date('Y-m-d') ?>" required>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="judul_catatan" class="form-label">Judul Catatan</label>
                        <input type="text" class="form-control" id="judul_catatan" name="judul" placeholder="Masukkan judul catatan..." required>
                    </div>

                    <div class="mb-3">
                        <label for="isi_catatan" class="form-label">Isi Catatan</label>
                        <textarea class="form-control" id="isi_catatan" name="catatan" rows="5" placeholder="Tulis catatan lengkap di sini..." required></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="tindak_lanjut" class="form-label">Tindak Lanjut (Opsional)</label>
                        <textarea class="form-control" id="tindak_lanjut" name="tindak_lanjut" rows="3" placeholder="Rencana tindak lanjut atau rekomendasi..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-info text-white">
                        <i class="bi bi-save me-1"></i>
                        Simpan Catatan
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- JavaScript for File Preview and Interactions -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Photo upload preview
    const fotoInput = document.getElementById('foto');
    const fotoPreview = document.getElementById('fotoPreview');

    if (fotoInput && fotoPreview) {
        fotoInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                if (file.type.startsWith('image/')) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        fotoPreview.innerHTML = `
                            <img src="${e.target.result}"
                                 class="img-fluid rounded"
                                 style="max-height: 200px; object-fit: cover;">
                        `;
                    };
                    reader.readAsDataURL(file);
                } else {
                    fotoPreview.innerHTML = `
                        <div class="bg-danger bg-opacity-10 rounded p-4">
                            <i class="bi bi-exclamation-triangle text-danger" style="font-size: 3rem;"></i>
                            <p class="text-danger mb-0 mt-2">File harus berupa gambar</p>
                        </div>
                    `;
                }
            }
        });
    }

    // Berkas upload preview
    const berkasInput = document.getElementById('berkas_file');
    const berkasPreview = document.getElementById('berkasPreview');

    if (berkasInput && berkasPreview) {
        berkasInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const fileExtension = file.name.split('.').pop().toLowerCase();
                let iconClass = 'bi-file-earmark';
                let iconColor = 'text-secondary';

                if (['jpg', 'jpeg', 'png', 'gif'].includes(fileExtension)) {
                    iconClass = 'bi-file-earmark-image';
                    iconColor = 'text-success';
                } else if (fileExtension === 'pdf') {
                    iconClass = 'bi-file-earmark-pdf';
                    iconColor = 'text-danger';
                } else if (['doc', 'docx'].includes(fileExtension)) {
                    iconClass = 'bi-file-earmark-word';
                    iconColor = 'text-primary';
                }

                berkasPreview.innerHTML = `
                    <div class="bg-light rounded p-4">
                        <i class="${iconClass} ${iconColor}" style="font-size: 3rem;"></i>
                        <p class="mb-1 mt-2 fw-bold">${file.name}</p>
                        <small class="text-muted">${(file.size / 1024 / 1024).toFixed(2)} MB</small>
                    </div>
                `;
            }
        });
    }

    // Reset modals when closed
    const modals = ['uploadFotoModal', 'uploadBerkasModal', 'addCatatanModal'];
    modals.forEach(modalId => {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.addEventListener('hidden.bs.modal', function() {
                const form = modal.querySelector('form');
                if (form) {
                    form.reset();

                    // Reset previews
                    if (modalId === 'uploadFotoModal' && fotoPreview) {
                        fotoPreview.innerHTML = `
                            <div class="bg-light rounded p-4">
                                <i class="bi bi-image text-muted" style="font-size: 3rem;"></i>
                                <p class="text-muted mb-0 mt-2">Preview foto akan muncul di sini</p>
                            </div>
                        `;
                    } else if (modalId === 'uploadBerkasModal' && berkasPreview) {
                        berkasPreview.innerHTML = `
                            <div class="bg-light rounded p-4">
                                <i class="bi bi-file-earmark text-muted" style="font-size: 3rem;"></i>
                                <p class="text-muted mb-0 mt-2">Preview file akan muncul di sini</p>
                            </div>
                        `;
                    }
                }
            });
        }
    });
});

// Delete functions
function deleteFoto(siswaId) {
    if (confirm('Apakah Anda yakin ingin menghapus foto siswa ini?')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/siswa-app/public/siswa/delete-foto/${siswaId}`;

        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = 'csrf_token';
        csrfToken.value = '<?= $csrf_token ?>';

        form.appendChild(csrfToken);
        document.body.appendChild(form);
        form.submit();
    }
}

function deleteBerkas(berkasId) {
    if (confirm('Apakah Anda yakin ingin menghapus berkas ini?')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/siswa-app/public/berkas/delete/${berkasId}`;

        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = 'csrf_token';
        csrfToken.value = '<?= $csrf_token ?>';

        form.appendChild(csrfToken);
        document.body.appendChild(form);
        form.submit();
    }
}

function deleteCatatan(catatanId) {
    if (confirm('Apakah Anda yakin ingin menghapus catatan ini?')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/siswa-app/public/catatan/delete/${catatanId}`;

        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = 'csrf_token';
        csrfToken.value = '<?= $csrf_token ?>';

        form.appendChild(csrfToken);
        document.body.appendChild(form);
        form.submit();
    }
}

function editCatatan(catatanId) {
    // Redirect to edit page
    window.location.href = `/siswa-app/public/catatan/edit/${catatanId}`;
}

// Form submission with loading state
document.addEventListener('submit', function(e) {
    const form = e.target;
    const submitBtn = form.querySelector('button[type="submit"]');

    if (submitBtn) {
        submitBtn.classList.add('loading');
        submitBtn.disabled = true;

        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="bi bi-arrow-clockwise me-1"></i>Memproses...';

        // Reset after 10 seconds if still loading
        setTimeout(() => {
            if (submitBtn.classList.contains('loading')) {
                submitBtn.classList.remove('loading');
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalText;
            }
        }, 10000);
    }
});
</script>
